<?php

namespace app\api\model\xycourse;

use think\Model;


class Scheduling extends Model
{
    

    // 表名
    protected $name = 'xycourse_scheduling';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_text',
        'starttime_text',
        'endtime_text'
    ];
    
    /**
     * params 请求参数
     */
    public static function getLists($params)
    {
        extract($params);
        $where = [
            'scheduling.type' => $type,
            'date'      => $date,
        ];

        $order = 'starttime asc';
        $scheduling = self::with(['coach','course'])->where($where);
        $scheduling = $scheduling->order($order);
        $cacheKey = 'schedulinglist-' . (isset($page) ? 'page' : 'all') . '-' . md5(json_encode($params));

        // 判断缓存
        $schedulingCache = cache($cacheKey);
        if ($schedulingCache) {
            // 存在缓存直接 返回
            $schedulingCache = json_decode($schedulingCache, true);
            return $schedulingCache ? : [];
        } 

        if (isset($page)) {
            $scheduling = $scheduling->paginate(20);
            $schedulingData = $scheduling->items();
        } else {
            $scheduling = $schedulingData = $scheduling->select();
        }

        $data = [];
        if ($schedulingData) {
            $data = collection($schedulingData);
        }

        if (isset($page)) {
            $scheduling->data = $data;
        } else {
            $scheduling = $data;
            cache($cacheKey, json_encode($scheduling), (600 + mt_rand(0, 300)));
        }

        return $scheduling;
    }

    
    public function getTypeList()
    {
        return ['league' => __('Type league'), 'private' => __('Type private')];
    }

    public function getDaysList()
    {
        return ['1' => __('当天'), '7' => __('7天'), '14' => __('14天'), '28' => __('28天'), '56' => __('56天')];
    }
    
    public function getWeeksList()
    {
        return ['1' => __('周一'),'2' => __('周二'),'3' => __('周三'),'4' => __('周四'),'5' => __('周五'),'6' => __('周六'),'0' => __('周日')];
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStarttimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['starttime']) ? $data['starttime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getEndtimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['endtime']) ? $data['endtime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setStarttimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setEndtimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function store()
    {
        return $this->belongsTo('\app\api\model\xycourse\store\Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function coach()
    {
        return $this->belongsTo('\app\api\model\xycourse\coach\Coach', 'coach_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function course()
    {
        return $this->belongsTo('\app\api\model\xycourse\Course', 'course_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


}
