<template>
	<view class="login p-50" :style="css.cpage+'min-height:'+$xyfun.xysys().windowHeight+'px'">
		<view class="title ts-48 m-t-50" :style="css.tcm">{{isRegisterMode ? '用户注册' : '欢迎登录'}}</view>
		<view class="logo tc">
			<image :src="$xyfun.image(common.appConfig.logo)" />
		</view>

		<!-- 手机号输入 -->
		<view class="user-list lh-70 flex p-tb-15 m-b-2 bc-w" v-if="loginMode === 'captcha'">
			<text class="list-name">手机号</text>
			<view class="flex r m-l-auto">
				<input class="br-10" :style="css.pbg" type="number" placeholder="请输入手机号" v-model="mobile" />
			</view>
		</view>

		<!-- 用户名输入 -->
		<view class="user-list lh-70 flex p-tb-15 m-b-2 bc-w" v-if="loginMode === 'password'">
			<text class="list-name">用户名</text>
			<view class="flex r m-l-auto">
				<input class="br-10" :style="css.pbg" type="text" placeholder="请输入用户名" v-model="username" />
			</view>
		</view>

		<!-- 密码输入 -->
		<view class="user-list lh-70 flex p-tb-15 m-b-2 bc-w" v-if="loginMode === 'password'">
			<text class="list-name">密码</text>
			<view class="flex r m-l-auto">
				<input class="br-10" :style="css.pbg" type="password" placeholder="请输入密码" v-model="password" />
			</view>
		</view>

		<!-- 注册模式下的手机号输入 -->
		<view class="user-list lh-70 flex p-tb-15 m-b-2 bc-w" v-if="loginMode === 'password' && isRegisterMode">
			<text class="list-name">手机号</text>
			<view class="flex r m-l-auto">
				<input class="br-10" :style="css.pbg" type="number" placeholder="请输入手机号(可选)" v-model="mobile" />
			</view>
		</view>

		<!-- 验证码输入 -->
		<view class="user-list lh-70 flex p-tb-15 m-b-2 bc-w" v-if="loginMode === 'captcha'">
			<text class="list-name">验证码</text>
			<view class="flex r m-l-auto">
				<input class="br-10" :style="css.pbg" type="number" placeholder="请输入验证码" v-model="captcha" />
				<button class="code ts-26 tc-w lh-78" :style="css.mcbg" @tap="getCaptcha" :disabled="disabledCode">{{codeText}}</button>
			</view>
		</view>

		<!-- 登录模式切换 -->
		<button :style="css.mcbg" class="ts-30 lh-30 p-25 tc-w m-t-20" @tap="toggleLoginMode">
			切换到{{loginMode === 'captcha' ? '密码' : '验证码'}}登录
		</button>

		<!-- 登录/注册按钮 -->
		<button :style="css.mcbg" class="ts-30 lh-30 p-25 tc-w m-t-50" @tap="handleSubmit" :disabled="submitDisabled">
			{{isRegisterMode ? '注册' : '登录'}}
		</button>

		<!-- 切换注册/登录模式 -->
		<button :style="css.mcbg" class="ts-30 lh-30 p-25 tc-w m-t-20" @tap="toggleRegisterMode">
			{{isRegisterMode ? '已有账号？去登录' : '没有账号？去注册'}}
		</button>

		<!-- 完善信息页面 -->
		<block v-if="isInfo">
			<view class="user-list lh-100 flex p-b-15 m-b-2 bc-w">
				<text class="list-name">头像</text>
				<view class="r m-l-auto">
					<button @tap="chooseImage">
						<image class="avatar" :src="avatar" v-if="avatar != ''" />
						<text class="xyicon icon-right m-l-15" v-else>选择头像</text>
					</button>
				</view>
			</view>
			<view class="user-list lh-70 flex p-tb-15 m-b-2 bc-w">
				<text class="list-name">昵称</text>
				<view class="flex r m-l-auto">
					<input class="br-10" :style="css.pbg" type="text" placeholder="请输入昵称~" @blur="blurNickName" maxlength="50" v-model="nickname" />
				</view>
			</view>
			<button :style="css.mcbg" class="ts-30 lh-30 p-25 tc-w m-t-50" @tap="profile()">确定</button>
			<button :style="css.mcbg" class="ts-30 lh-30 p-25 tc-w m-t-50" @tap="$Router.back()">跳过</button>
		</block>

		<!-- 用户协议 -->
		<view class="tips flex p-tb-30 tc m-t-30 lh-50" :style="css.tcl" v-if="!isInfo">
			<view class="agree flex " @tap="isAgree=!isAgree">
				<text class="xyicon icon-radio-a ts-30 flex tb" v-if="isAgree"></text>
				<text class="xyicon icon-radio ts-30 flex tb" v-else></text>
				<text class="m-l-10">我已阅读并同意</text>
			</view>
			<text :style="css.tcmc" @tap="see(1)">《用户协议》</text> 和 <text :style="css.tcmc" @tap="see(2)">《隐私政策》</text>
		</view>
	</view>
</template>
<script>
	import { mapState} from 'vuex';
	import graceChecker from '@/utils/graceChecker';
	import http_config from '@/config/http';
	export default {
		data() {
			return {
				isInfo: false, // 是否需要完善信息
				isAgree: false, // 是否同意协议
				isRegisterMode: false, // 是否为注册模式
				loginMode: 'password', // 登录模式：password 或 captcha
				css: {},

				// 表单数据
				mobile: '',
				username: '',
				password: '',
				captcha: '',
				avatar: '',
				nickname: '',

				// 状态控制
				submitDisabled: false,
				disabledCode: false,
				codeText: '获取验证码',
				countdown: 0,
				userInfo: {},
			};
		},
		computed: {
			...mapState(['common','user'])
		},
		async onLoad() {
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();

			if(this.user.isLogin){
				this.$xyfun.back('已经登录');
			}
		},
		methods: {

			blurNickName(e) {
				if (e.detail.value) this.nickname = e.detail.value;
			},

			// 切换登录模式
			toggleLoginMode() {
				this.loginMode = this.loginMode === 'captcha' ? 'password' : 'captcha';
				this.clearForm();
			},

			// 切换注册/登录模式
			toggleRegisterMode() {
				this.isRegisterMode = !this.isRegisterMode;
				this.clearForm();
			},

			// 清空表单
			clearForm() {
				this.mobile = '';
				this.username = '';
				this.password = '';
				this.captcha = '';
			},

			// 主要提交处理
			handleSubmit() {
				if (!this.isAgree) {
					return this.$xyfun.msg('请阅读并同意《用户协议》和《隐私政策》');
				}

				if (this.isRegisterMode) {
					this.register();
				} else {
					this.login();
				}
			},

			// 登录
			login() {
				if (this.loginMode === 'password') {
					this.passwordLogin();
				} else {
					this.captchaLogin();
				}
			},

			// 注册
			register() {
				if (this.loginMode === 'password') {
					this.passwordRegister();
				} else {
					this.captchaRegister();
				}
			},

			// 密码登录
			passwordLogin() {
				if (!this.username || !this.password) {
					return this.$xyfun.msg('请输入用户名和密码');
				}

				this.submitDisabled = true;
				this.$api.post({
					url: '/user/passwordLogin',
					loadingTip: '登录中...',
					data: {
						username: this.username,
						password: this.password
					},
					success: res => {
						this.handleLoginSuccess(res);
					},
					fail: res => {
						this.submitDisabled = false;
					}
				});
			},

			// 验证码登录
			captchaLogin() {
				if (!this.mobile || !this.captcha) {
					return this.$xyfun.msg('请输入手机号和验证码');
				}

				this.submitDisabled = true;
				this.$api.post({
					url: '/user/login',
					loadingTip: '登录中...',
					data: {
						mobile: this.mobile,
						captcha: this.captcha
					},
					success: res => {
						this.handleLoginSuccess(res);
					},
					fail: res => {
						this.submitDisabled = false;
					}
				});
			},

			// 密码注册
			passwordRegister() {
				if (!this.username || !this.password) {
					return this.$xyfun.msg('请输入用户名和密码');
				}

				if (this.password.length < 6) {
					return this.$xyfun.msg('密码长度不能少于6位');
				}

				this.submitDisabled = true;
				this.$api.post({
					url: '/user/passwordRegister',
					loadingTip: '注册中...',
					data: {
						username: this.username,
						password: this.password,
						mobile: this.mobile
					},
					success: res => {
						this.handleLoginSuccess(res);
					},
					fail: res => {
						this.submitDisabled = false;
					}
				});
			},

			// 验证码注册
			captchaRegister() {
				if (!this.mobile || !this.captcha) {
					return this.$xyfun.msg('请输入手机号和验证码');
				}

				this.submitDisabled = true;
				this.$api.post({
					url: '/user/register',
					loadingTip: '注册中...',
					data: {
						mobile: this.mobile,
						captcha: this.captcha
					},
					success: res => {
						this.handleLoginSuccess(res);
					},
					fail: res => {
						this.submitDisabled = false;
					}
				});
			},

			// 处理登录成功
			handleLoginSuccess(res) {
				this.submitDisabled = false;
				if (res.userInfo.is_info == 1) {
					// 需要完善信息
					this.userInfo = res.userInfo;
					this.isInfo = true;
					this.$store.dispatch('user/login', res);
				} else {
					// 直接登录成功
					this.$store.dispatch('user/login', res);
					this.$Router.back();
				}
			},

			// 获取验证码
			getCaptcha() {
				if (!this.mobile) {
					return this.$xyfun.msg('请输入手机号');
				}

				if (!/^1[3-9]\d{9}$/.test(this.mobile)) {
					return this.$xyfun.msg('请输入正确的手机号');
				}

				this.$api.post({
					url: '/user/captcha',
					data: {
						mobile: this.mobile
					},
					success: res => {
						this.$xyfun.msg('验证码已发送');
						this.startCountdown();
					}
				});
			},

			// 开始倒计时
			startCountdown() {
				this.disabledCode = true;
				this.countdown = 60;
				this.codeText = `${this.countdown}s`;

				const timer = setInterval(() => {
					this.countdown--;
					this.codeText = `${this.countdown}s`;

					if (this.countdown <= 0) {
						clearInterval(timer);
						this.disabledCode = false;
						this.codeText = '获取验证码';
					}
				}, 1000);
			},

			// 图片处理-选择图片
			async chooseImage() {
				var that = this;
				console.log('选择图片');
				uni.chooseImage({
					count: 1,
					sizeType: ["original", "compressed"],
					sourceType: ["album"],
					success: res => {
						that.uploadImage(res.tempFilePaths[0])
					}
				});
			},
			
			// 上传图片
			async uploadImage(url) {
				var that = this;
				uni.showLoading({
					title:'图片上传中...'
				})
				return new Promise((resolve) => {
					uni.uploadFile({
						url: http_config.base_url + "/api/common/upload",
						filePath: url,
						name: "file",
						header: { token: that.userInfo.token},
						success: res => {
							res = JSON.parse(res.data);
							that.avatar = res.data.fullurl;
							resolve(res.data.fullurl);
						},
						fail: res =>{
							uni.hideLoading();
							that.$xyfun.msg('图片上传失败！');
							that.avatar = '';
							console.log(res);
						},
						complete: () => {
							uni.hideLoading();
						}
					});
				}).catch(e => {
					console.log(e);
				});
			},
			
			see(type){
				if(type==1 && this.common.appConfig.agreement > 0){
					this.$xyfun.to('/pages/article/detail?id='+this.common.appConfig.agreement);
				}
				if(type==2 && this.common.appConfig.privacy > 0){
					this.$xyfun.to('/pages/article/detail?id='+this.common.appConfig.privacy);
				}
			},
			
			//完善信息
			profile(){
				
				var data = {
					avatar:this.avatar,
					nickname:this.nickname,
				}
				
				//定义表单规则
				var rule = [
					{ name: 'avatar', checkType: 'string', checkRule: '1,300', errorMsg: '头像不能为空' },
					{ name: 'nickname', checkType: 'string', checkRule: '2,20', errorMsg: '昵称为2-20个字符' },
					
				];
				
				//进行表单检查
				var checkRes = graceChecker.check(data, rule);
				
				if (checkRes) {
					this.$api.post({
						url: '/user/profile',
						loadingTip:'加载中...',
						data: {
							nickname: this.nickname,
							avatar: this.avatar,
						},
						success: res => {
							this.$store.dispatch('user/login', res);
							this.$Router.back();
						}
					});
				}else {
					this.$xyfun.msg(graceChecker.error);
					this.submitDisabled = false;
				}
				
				
			}
		}
	};
</script>

<style scoped lang="scss">
	.logo image{width: 180rpx;height: 180rpx;margin: 100rpx;}
	button::after{border: none;}
	.avatar{width: 100rpx;height: 100rpx;}
	.user-list{
		flex-wrap: nowrap;
		.list-name{width: 20%;flex-shrink: 0;}
		.code{flex-shrink: 0;width: 180rpx;height: 78rpx;line-height: 78rpx;text-align: center;margin-left: 20rpx;}
		.r{
			width: 100%;
			input{width: 100%;vertical-align: middle;line-height: 78rpx;height: 78rpx;padding-left: 20rpx;}
			button{padding: 0;margin: 0;background: none;float: right;border-radius: 0;width: 100%;text-align: right;}
		}
	}
	.tips{justify-content: center;}
</style>

<style>
.page {
  background-color: #ffffff;
  color: #000000;
}
.input {
  border: 1px solid #007bff;
  border-radius: 5px;
  padding: 10px;
  margin: 10px;
  background-color: #f8f9fa;
}
.button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  margin: 10px;
  cursor: pointer;
}
.button:hover {
  background-color: #0056b3;
}
</style>

