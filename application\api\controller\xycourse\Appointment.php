<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;

use app\api\model\xycourse\Appointment as AppointmentModel;


/**
 * XYcourse 课程包预约接口
 */
class Appointment extends Api
{
    protected $noNeedLogin = ['lists'];
    protected $noNeedRight = ['*'];
    

    /**
	 * 列表
	 */
	public function lists()
    {
    	$params = $this->request->post();
        $data = AppointmentModel::getLists($params);
        $this->success('预约列表', $data);
    }
    
	public function add()
    {
        $params = $this->request->post();
        $order = AppointmentModel::addAppointment($params);
        $this->success('预约成功', $order);
    }

    public function cancel()
    {
        $params = $this->request->post();
        $params['user_id'] = $this->auth->id;
        $this->success('取消成功', AppointmentModel::cancelAppointment($params));
    }
	
    /**
     * 预约确认
     */
    public function verify()
    {
        $params = $this->request->post();
        $params['user_id'] = $this->auth->id;
        $data = AppointmentModel::verifyAppointment($params);
        $this->success('操作成功', $data);
    }
	
}