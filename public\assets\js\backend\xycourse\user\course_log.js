define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xycourse/user/course_log/index' + location.search,
                    multi_url: 'xycourse/user/course_log/multi',
                    import_url: 'xycourse/user/course_log/import',
                    table: 'xycourse_user_course_log',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'coach.realname',operate: 'LIKE', title: __('核销老师'),formatter:function(v,row){
                                if(v == null){
                                    return '暂未绑定';
                                }else{
                                    var html = '<div style="display:flex"><img width="50px" height="50px" src="'+row.coach.headimage+'" /><p style="text-align:left;line-height:20px;margin-top:5px;margin-left:10px">'+row.coach.realname+'(ID:'+row.coach.id+')<br/>'+row.coach.mobile+'</p></div>';
                                    return html;
                                }
                            }
                        },
                        {field: 'user.nickname',operate: 'LIKE', title: __('核销会员'),formatter:function(v,row){
                                if(v == null){
                                    return '暂未绑定';
                                }else{
                                    var html = '<div style="display:flex"><img width="50px" height="50px" src="'+row.user.avatar+'" /><p style="text-align:left;line-height:20px;margin-top:5px;margin-left:10px">'+row.user.nickname+'(ID:'+row.user.id+')<br/>'+row.user.mobile+'</p></div>';
                                    return html;
                                }
                            }
                        },
                        {field: 'course.name', title: __('核销课程')},
                        {field: 'num', title: __('核销次数')},
                        {field: 'createtime', title: __('核销时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                       // {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
