import store from '@/store'
import $api from './request'
import xyfun from './xyfun'

export default {
	async onLoad() {
		
		var options = this.$Route.query;
		
		console.log('进入options',options);
		
		// #ifdef MP
		if (options?.scene) {
			options = xyfun.sceneDecode(options.scene);
		}
		// #endif
		
		if (options?.spm) {
			var spmArr = (options.spm).split('.');
			if (spmArr[0] != '0') {
				if (store.state.user.isLogin) {
					$api.post({
						url: '/share/add',
						data: {spm: options.spm},
						success: res => {
							console.log(res);
						},
						fail: res =>{
							console.log(res);
						}
					});
				} else {
					uni.setStorageSync('xycourse:spm', options.spm);
				}
			}
			
		}
	},
	
	
	// #ifdef MP-WEIXIN
	onShareAppMessage() {
		
		var shareInfo = store.state.common.shareConfig;
		var imageUrl = xyfun.image(shareInfo.image);
		
		return {
			title: shareInfo.title,
			path: shareInfo.path,
			imageUrl: imageUrl,
			success(res) {
				console.log(res);
			},
			fail(res) {
				console.log(res);
			}
		}
		
	},
	// #endif
}
