<?php

namespace app\api\model\xycourse\store;

use think\Model;
use app\api\model\xycourse\store\Service;

class Store extends Model
{

    // 表名
    protected $name = 'xycourse_store';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';


    // 追加属性
    protected $append = [
        'status_text'
    ];

    public function getImagesAttr($value)
    {
        $imagesArray = [];
        if (!empty($value)) {
            $imagesArray = explode(',', $value);
            foreach ($imagesArray as &$v) {
                $v = cdnurl($v, true);
            }
            return $imagesArray;
        }
        return $imagesArray;
    }

    public function getLogoAttr($value)
    {
        if (!empty($value)) return cdnurl($value, true);
    }

    public static function getDetail($id)
    {
        $store = self::where(['id'=>$id])->find();
        if($store){
            $store['service'] = Service::getServiceList(['ids'=>$store['service_ids']]);
        }
        return $store;
    }

    /**
     * params 请求参数
     */
    public static function getLists($params)
    {
        extract($params);
        $where = [
            'status' => 'normal'
        ];

        $order = 'weigh desc,id desc';

        if (isset($search) && $search !== '') {
            $where['name'] = ['like', "%$search%"];
        }

        if (isset($ids) && $ids !== '') {
            $idsArray = explode(',', $ids);
            $where['id'] = ['in', $idsArray];
        }
       

        if(isset($id) && $id > 0){
            $where['id'] = $id;
        }

        $store = self::where($where);

        $store = $store->order($order);

        $cacheKey = 'storelist-' . (isset($page) ? 'page' : 'all') . '-' . md5(json_encode($params));

        // 判断缓存
        $storeCache = cache($cacheKey);
        if ($storeCache) {
            // 存在缓存直接 返回
            $storeCache = json_decode($storeCache, true);
            return $storeCache ? : [];
        } 

        if (isset($page)) {
            $store = $store->paginate($per_page ?? 10);
            $storeData = $store->items();
        } else {
            $store = $storeData = $store->select();
        }

        $data = [];
        if ($storeData) {
            $data = collection($storeData);
        }

        if (isset($page)) {
            $store->data = $data;
        } else {
            $store = $data;
            cache($cacheKey, json_encode($store), (600 + mt_rand(0, 300)));
        }

        return $store;
    }
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    
    public function getStatusList()
    {
        return ['normal' => __('显示'),'hidden' => __('隐藏')];
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    

}
