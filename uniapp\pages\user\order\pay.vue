<template>
	<view class="pay" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		<view class="p-lr-30 p-tb-15 tc-r bc-w m-b-2 ts-26 tc" :style="css.tcp">提示：退订需提前订单预定日期{{common.appConfig.returnhour}}小时,预定当天场地不能退订。</view>
		<view class="item-list p-lr-30 br-10 bc-w">
			<view v-for="(g,index) in orderDetail.item" :key="index" :style="css.blpc" :class="'item p-tb-30 flex '+(index == orderDetail.item.length-1?'':'bl-b')">
				<view class="l ovh br-10">
					<image :src="orderDetail.storelogo" mode="widthFix" />
				</view>
				<view class="r psr m-l-20">
					<view class="tb lh-40 goods-title">
						{{orderDetail.storename}}
					</view>
					<view class="venue m-t-10">
						{{orderDetail.venuename}} - {{g.area}}
					</view>
					<view class="date m-t-20 ts-26" :style="css.tcl">
						{{g.date}} {{g.week}} {{g.starttime}}-{{g.endtime}}
					</view>
					<view class="price-box flex psa lh-32">
						<text :style="css.tcp">￥</text>
						<text :style="css.tcp" class="ts-32 tb">{{ g.price }}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="pay-list">
			<view class="item flex p-30 m-b-2 lh-50 bc-w" v-for="(item, index) in payList" :key="index" v-if="item.state">
				<view class="l flex">
					<text :class="'xyicon icon-'+item.icon+' ts-40 m-r-15 lh-40 p-t-5'"></text>
					<text class="lh-50">{{item.name}}</text>
				</view>
				<view class="r tb m-l-auto" @tap="payMethodSelect(index)">
					<text class="xyicon icon-radio-a ts-32 lh-50" v-if="item.select"></text>
					<text class="xyicon icon-radio ts-32 lh-50" v-else></text>
				</view>
			</view>
		</view>
		
		<view class="bottom-fixed flex tc bc-w">
			<view class="p-lr-30 p-tb-15 flex tc wa">
				<view class="lh-42 p-t-15 m-r-30"><text>需支付：</text><text class="ts-26" :style="css.tcp">¥</text><text class="ts-36" :style="css.tcp">{{ orderDetail.total_amount || "0.00" }}</text></view>
				<view class="action flex m-l-auto">
					<view class="sub-order m-l-auto tc-w" :style="css.mcbg" @tap="subPay()">
						确认支付
					</view>
				</view>
			</view>
		</view>
		
		
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import Pay from '@/utils/pay';
	import xyStoreRow from '@/components/xy-store/row';
	export default {
		components: {
			xyStoreRow,
		},
		data() {
			return {
				css:{},
				disabled:false,
				isLoading:true,
				orderDetail:null, //订单详情
				payModelShow:false,
				payList:[{
					name: '余额支付',
					method: 'balance',
					icon: 'balance',
					state: true,
					select: false
				},
				{
					name: '微信支付',
					method: 'wechat',
					icon: 'wechat',
					state: true,
					select: true
				}]
			}
		},
		computed: {
			...mapState(['common','user'])
		},
		async onLoad() {
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		methods: {
			
			async loadData(){
				this.$api.post({
					url: '/order/init',
					loadingTip:'加载中...',
					data: {
						order_id:this.$Route.query.order_id,
					},
					success: res => {
						this.isLoading = false;
						this.orderDetail = res;
					},
					fail: res => {
						setTimeout(()=>{
							uni.navigateBack();
						},2000)
					}
				});
			},
			
			jump(path, parmas) {
			  this.$Router.push({
			    path: path,
			    query: parmas,
			  });
			},
			
			//支付方式选择
			payMethodSelect(key){
				
				this.payList.map((value,index) => {
				　　if(index == key){
						value.select = !value.select;
					}else{
						value.select = false;
					}
				});
			},
			
			//确认支付
			subPay(){
				if(this.orderDetail != null && !this.disabled){
					var pay_type = '';
					this.payList.map((value) => {
					　　if(value.select){
							pay_type = value.method;
						}
					});
					if (!pay_type) {
						this.$xyfun.msg('请选择支付方式');
					}else{
						//发起支付
						var pay = new Pay(pay_type, this.orderDetail, 'order');
						pay.payMehod().then((res)=>{
							this.disabled = true;
							pay.payResult(res);
						},(res)=>{
							this.disabled = false;
							if(pay_type != 'balance'){
								pay.payResult(res);
							}
						});
					}
				}
			}
			
			
		}
	}
</script>

<style scoped lang="scss">
	.line{height: 4rpx;width: 34%;margin: 0 auto;}
	
	.item-list{
		.item{
			.l{
				width: 150rpx;height: 150rpx;
				image{width: 150rpx;height: 150rpx;}
			}
			.r{
				width: 510rpx;
				.price-box{position: absolute; right: 0;top: 0;}
			}
		}
	}
	.bottom-fixed{
		.sub-order{width: 260rpx;height: 74rpx;border-radius: 37rpx;line-height: 74rpx;}
	}
	
	.pay-model-box{width: 80%;}
</style>