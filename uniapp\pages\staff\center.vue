<template>
	<view class="staff" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		<view class="top p-50 flex" :style="css.nbg">
			<view class="staff-info flex tc-w">
				<image class="headimage" :src="staffInfo.headimage" />
				<view class="m-l-15">
					<view class="tb m-t-10">{{staffInfo.realname}}</view>
					<view>{{staffInfo.mobile}}</view>
				</view>
			</view>
			<view class="home bc-w p-lr-25 flex m-l-auto" :style="css.tcmc" @tap="$xyfun.to('/pages/index')"><text class="xyicon icon-home m-r-10"></text>返回</view>
		</view>
		
		<view class="menu-list m-50 flex" :style="css.mbg">
			<view class="item m-b-40 tc bc-w br-20" v-for="(item,index) in menuList" :key="index">
				<view v-if="item.url == 'scan'" @tap="scan()">
					<image :src="item.image" />
					<view class="m-t-20 ts-28">{{item.name}}</view>
				</view>
				<view @tap="$xyfun.to(item.url)" v-else>
					<image :src="item.image" />
					<view class="m-t-20 ts-28">{{item.name}}</view>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	import { mapState,mapActions } from 'vuex';
	export default {
		data() {
			return {
				css:{},
				role:'staff',
				isLoading:true,
				staffInfo:{},
				menuList:[
					{name:'扫会员码',url:'scan',image:'/static/images/staff/menu0.png'},
					{name:'预约管理',url:'/pages/staff/appointment',image:'/static/images/staff/menu1.png'},
				],
			}
		},
		computed: {
			...mapState(['common','user'])
		},
		onLoad() {
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		methods: {
			...mapActions('user',{'getStaffInfo':'getStaffInfo'}),
			
			loadData(){
				this.getStaffInfo().then(res => {
						this.isLoading = false;
						this.staffInfo = res;
					},()=>{
						setTimeout(()=>{
							uni.navigateBack();
						},3000)
					})
				
			},
			
			//扫会员码
			scan(){
				
				var that = this;
				uni.scanCode({
					success: function (res) {
						
						if(res.scanType == 'QR_CODE' && res.result !=''){
							
							var result = res.result.split("_");
							var user_id = result.length ? result[0] : 0;
							
							if(user_id > 0){
								var curTime =Date.parse(new Date()) / 1000;
								var time = result[1];
								if(curTime - time > 10*60){
									that.$xyfun.msg('会员码已过期！');
								}else{
									that.$xyfun.to('/pages/staff/member?user_id='+user_id);
								}
							}else{
								that.$xyfun.msg('错误的会员码！');
							}
						}else{
							that.$xyfun.msg('错误的会员码！');
						}
					}
				});
			}
			
		}
	}
</script>

<style scoped lang="scss">
	.staff{
		
		.top{
			.staff-info{
				.headimage{width: 100rpx;height: 100rpx;border-radius: 50rpx;}
			}
			.home{height: 54rpx;border-radius: 27rpx;line-height: 54rpx;margin-top: 23rpx;}
		}
		
		.menu-list{
			.item{width: 305rpx;height: 305rpx;}
			.item:nth-child(2n){margin-left: auto;}
			image{width: 90rpx;height: 90rpx; margin-top: 90rpx;}
		}
		
	}
</style>