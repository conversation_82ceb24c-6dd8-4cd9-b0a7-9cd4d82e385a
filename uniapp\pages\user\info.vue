<template>
	<view class="info" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'">
		
		<view class="user-list lh-60 flex p-lr-30 p-tb-40 m-b-2">
			<view class="list-name tb">基本信息</view>
			<view class="save-btn ts-30 tc lh-30 p-15 m-l-auto bc-w" @tap="logout()">退出登录</view>
		</view>
		
		<view class="user-list lh-100 flex p-lr-30 p-tb-15 m-b-2 bc-w">
			<text class="list-name">头像</text>
			<view class="r m-l-auto">
				<!-- #ifdef MP-WEIXIN -->
				<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
					<image class="avatar" :src="$xyfun.image(userInfo.avatar)" />
					<text class="xyicon icon-right m-l-15"></text>
				</button>
				<!-- #endif -->
			</view>
		</view>
		
		<view class="user-list lh-70 flex p-lr-30 p-tb-15 m-b-2 bc-w">
			<text class="list-name">昵称</text>
			<view class="flex r m-l-auto">
				<input class="br-10 p-20 lh-30" :style="css.pbg" placeholder="请输入昵称~" maxlength="50" v-model="userInfo.nickname" />
			</view>
		</view>
		<view class="user-list lh-70 flex p-lr-30 p-tb-15 m-b-2 bc-w">
			<text class="list-name">手机号</text>
			<view class="flex r m-l-auto">
				<input class="br-10 p-20 lh-30" :style="css.pbg" disabled="true" v-model="userInfo.mobile" maxlength="50" />
			</view>
		</view>
		
		<view class="bottom-fixed p-b-50" :style="css.pbg">
			<button :style="css.mcbg" class="ts-30 lh-30 p-25 tc-w m-t-50 m-50" @tap="editUserInfo()">保存</button>
		</view>
		
	</view>
</template>

<script>
	import { mapState,mapActions } from 'vuex';
	import graceChecker from '@/utils/graceChecker';
	import http_config from '@/config/http'; 
	export default {
		
		data() {
			return {
				css:{},
				editInfoDisabled:false,
				userInfo:{},
			}
		},
		computed: {
			...mapState(['common','user'])
		},
		async onLoad() {
			
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.getUserInfo().
				then(res => {
					this.userInfo = res.userInfo;
				}).catch(e => {
					console.log(e);
				});
		},
		onPullDownRefresh() {
			
		},
		methods: {
			...mapActions('user',{'getUserInfo':'getInfo'}),
			
			logout(){
				this.$api.post({
					url: '/user/logout',
					success: () => {
						this.$store.dispatch('user/logout');
						this.$xyfun.msg('退出成功');
						this.$xyfun.to('/pages/index',true);
					}
				});
			},
			
			onChooseAvatar(e) {
				this.uploadImage(e.detail.avatarUrl);
				this.editInfoDisabled = true;
			},
			
			// 图片处理-选择图片
			async chooseImage() {
				var that = this;
				console.log('选择图片');
				uni.chooseImage({
					count: 1, 
					sizeType: ["original", "compressed"], 
					sourceType: ["album"], 
					success: res => {
						console.log('选择图片',res);
						that.uploadImage(res.tempFilePaths[0])
					}
				});
			},
			
			// 上传图片
			async uploadImage(url) {
				var that = this;
				uni.showLoading({
					title:'图片上传中...'
				})
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: http_config.base_url + "/api/common/upload",
						filePath: url,
						name: "file",
						header: { token: that.user.info.token },
						success: res => {
							console.log('res',res);
							res = JSON.parse(res.data);
							that.userInfo.avatar = res.data.fullurl;
							resolve(res.data.fullurl);
						},
						fail: res =>{
							that.$xyfun.msg('图片上传失败！');
						},
						complete: res => {
							uni.hideLoading();
						}
					});
				}).catch(e => {
					console.log(e);
				});
			},
			
		
			
			// 修改信息
			editUserInfo() {
				
				var data = {
					avatar:this.userInfo.avatar,
					nickname:this.userInfo.nickname,
					mobile:this.userInfo.mobile
				}
				
				//定义表单规则
				var rule = [
					{ name: 'avatar', checkType: 'string', checkRule: '1,300', errorMsg: '头像不能为空' },
					{ name: 'nickname', checkType: 'string', checkRule: '2,20', errorMsg: '昵称为2-20个字符' },
					{ name: 'mobile', checkType: 'phoneno', errorMsg: '请填写正确的手机号' },
				];
				
				//进行表单检查
				var checkRes = graceChecker.check(data, rule);
				
				if (checkRes) {
					this.$api.post({
						url: '/user/profile',
						loadingTip:'加载中...',
						data: data,
						success: (res) => {
							this.$xyfun.msg('信息修改成功');
							this.$store.dispatch('user/info', res);
							uni.navigateBack();
						}
					});
				}else {
					this.$xyfun.msg(graceChecker.error);
					this.submitDisabled = false;
				}
				
				
			},
		}
	}
</script>

<style scoped lang="scss">
	.avatar{width: 100rpx;height: 100rpx;}
	.user-list .r{
		width: 70%;
		input{width: 100%;}
		button{padding: 0;margin: 0;background: none;float: right;border-radius: 0;}
		button::after{border: none;}
	}
	.save-btn{width: 140rpx;border-radius: 35rpx;}
</style>
