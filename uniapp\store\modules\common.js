/**
 * 
 * XYcourse 系统初始化通用配置
 * <AUTHOR>
 * 
 **/  
  
import api from '../../utils/request'; 

export default {
	namespaced: true,
	state: {
		appStyle: {}, // 通用样式
		appConfig: {}, // 通用配置
		shareConfig: {
			title:'',
			path:'/pages/index',
			image:'',
			desc:''
		}, // 分享配置
		sysShareConfig:{},//系统设置分享信息
		tabBarList:{}, // 自定义底部导航
		css:{},//通用样式
	},
	mutations: {
		setShareConfig(state, shareInfo) {
			state.shareConfig = shareInfo;
			console.log('重置之后的分享信息：',state.shareConfig);
		}
	},
	actions: {
		async update({}) {
			// #ifdef MP
			const mp = uni.getUpdateManager();
			mp.onCheckForUpdate((res) => {
				console.log(res)
			});
			mp.onUpdateReady((res) => {
				console.log(res)
				uni.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					success(show) {
						if (show.confirm) {
							mp.applyUpdate();
						}
					}
				});
			});
			mp.onUpdateFailed((res) => {
				console.log(res)
			});
			// #endif
		},
		async checkToken({rootState}) {
			return new Promise((resolve, reject) => {
				api.post({
					url: '/token/check',
					success: res => {
						if (res.token) {
							rootState.user.isLogin = true;
							rootState.user.info = uni.getStorageSync('xycourse:user');
						}else{
							rootState.user.isLogin = false;
							rootState.user.info = null;
							uni.removeStorageSync('xycourse:user');
						}
					
						resolve(res);
					},
					fail: res => {
						reject(res)
					}
				});
			})
		},
		async init({state}){
			
			return new Promise((resolve, reject) => {
				api.get({
					url: '/common/init',
					success: res => {
						state.appConfig = res.appConfig;
						state.appStyle = res.appStyle;
						state.sysShareConfig = res.shareConfig;
						state.tabBarList = res.tabBarList;
						resolve(res);
					},
					fail: res => {
						reject(res)
					}
				});
			})
			
		},
		async share({commit}, shareInfo) {
			commit('setShareConfig', shareInfo);
		},
	}
};