<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录注册测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            cursor: pointer;
        }
        .tab.active {
            background-color: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户注册登录测试</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('captcha')">验证码模式</div>
            <div class="tab" onclick="switchTab('password')">密码模式</div>
        </div>

        <!-- 验证码模式 -->
        <div id="captcha-tab" class="tab-content active">
            <h3>验证码注册/登录</h3>
            <div class="form-group">
                <label for="mobile">手机号:</label>
                <input type="text" id="mobile" placeholder="请输入手机号" value="13800138000">
            </div>
            <div class="form-group">
                <label for="captcha">验证码:</label>
                <input type="text" id="captcha" placeholder="请输入验证码">
                <button onclick="getCaptcha()" id="captcha-btn">获取验证码</button>
            </div>
            <button onclick="captchaRegister()">验证码注册</button>
            <button onclick="captchaLogin()">验证码登录</button>
        </div>

        <!-- 密码模式 -->
        <div id="password-tab" class="tab-content">
            <h3>密码注册/登录</h3>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" placeholder="请输入用户名" value="testuser">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" placeholder="请输入密码" value="123456">
            </div>
            <div class="form-group">
                <label for="reg-mobile">手机号(可选):</label>
                <input type="text" id="reg-mobile" placeholder="请输入手机号" value="13800138000">
            </div>
            <button onclick="passwordRegister()">密码注册</button>
            <button onclick="passwordLogin()">密码登录</button>
        </div>

        <!-- 已登录用户操作 -->
        <div style="margin-top: 30px; border-top: 1px solid #eee; padding-top: 20px;">
            <h3>已登录用户操作</h3>
            <div class="form-group">
                <label for="nickname">昵称:</label>
                <input type="text" id="nickname" placeholder="请输入昵称" value="测试用户">
            </div>
            <button onclick="updateProfile()">更新资料</button>
            <button onclick="refreshUser()">刷新用户信息</button>
            <button onclick="logout()">退出登录</button>
        </div>

        <div id="result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://************:818/api/xycourse';
        let currentToken = '';
        let countdown = 0;

        function switchTab(tab) {
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(t => t.classList.remove('active'));
            
            document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');
            document.getElementById(`${tab}-tab`).classList.add('active');
        }

        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        function apiRequest(endpoint, data) {
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            };
            
            if (currentToken) {
                headers['token'] = currentToken;
            }

            const formData = new URLSearchParams(data);

            return fetch(`${API_BASE}${endpoint}`, {
                method: 'POST',
                headers: headers,
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    if (data.data && data.data.userInfo && data.data.userInfo.token) {
                        currentToken = data.data.userInfo.token;
                        showResult(`成功: ${data.msg}\n用户信息: ${JSON.stringify(data.data, null, 2)}`);
                    } else {
                        showResult(`成功: ${data.msg}\n响应数据: ${JSON.stringify(data, null, 2)}`);
                    }
                } else {
                    showResult(`错误: ${data.msg}`, false);
                }
            })
            .catch(error => {
                showResult(`网络错误: ${error.message}`, false);
            });
        }

        function getCaptcha() {
            const mobile = document.getElementById('mobile').value;
            if (!mobile) {
                showResult('请输入手机号', false);
                return;
            }

            apiRequest('/user/captcha', { mobile });
            
            // 开始倒计时
            countdown = 60;
            const btn = document.getElementById('captcha-btn');
            btn.disabled = true;
            
            const timer = setInterval(() => {
                btn.textContent = `${countdown}s`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.textContent = '获取验证码';
                }
            }, 1000);
        }

        function captchaRegister() {
            const mobile = document.getElementById('mobile').value;
            const captcha = document.getElementById('captcha').value;
            
            if (!mobile || !captcha) {
                showResult('请输入手机号和验证码', false);
                return;
            }

            apiRequest('/user/register', { mobile, captcha });
        }

        function captchaLogin() {
            const mobile = document.getElementById('mobile').value;
            const captcha = document.getElementById('captcha').value;
            
            if (!mobile || !captcha) {
                showResult('请输入手机号和验证码', false);
                return;
            }

            apiRequest('/user/login', { mobile, captcha });
        }

        function passwordRegister() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const mobile = document.getElementById('reg-mobile').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', false);
                return;
            }

            apiRequest('/user/passwordRegister', { username, password, mobile });
        }

        function passwordLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', false);
                return;
            }

            apiRequest('/user/passwordLogin', { username, password });
        }

        function updateProfile() {
            const nickname = document.getElementById('nickname').value;
            
            if (!nickname) {
                showResult('请输入昵称', false);
                return;
            }

            if (!currentToken) {
                showResult('请先登录', false);
                return;
            }

            apiRequest('/user/profile', { nickname });
        }

        function refreshUser() {
            if (!currentToken) {
                showResult('请先登录', false);
                return;
            }

            apiRequest('/user/refresh', {});
        }

        function logout() {
            if (!currentToken) {
                showResult('请先登录', false);
                return;
            }

            apiRequest('/user/logout', {})
            .then(() => {
                currentToken = '';
            });
        }
    </script>
</body>
</html>
