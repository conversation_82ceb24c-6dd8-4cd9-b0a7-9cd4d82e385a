<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-type" data-rule="required" class="form-control selectpicker" name="row[type]">
                {foreach name="typeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="distribution"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw_sn')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-withdraw_sn" data-rule="required" class="form-control" name="row[withdraw_sn]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Apply_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-apply_money" data-rule="required" class="form-control" step="0.01" name="row[apply_money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rate" data-rule="required" class="form-control" step="0.01" name="row[rate]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_money" data-rule="required" class="form-control" step="0.01" name="row[service_money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" data-rule="required" class="form-control" step="0.01" name="row[money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Audit_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-audit_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[audit_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-payment_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[payment_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Realname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-realname" data-rule="required" class="form-control" name="row[realname]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" data-rule="required" class="form-control" name="row[mobile]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_name" data-rule="required" class="form-control" name="row[account_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_type" data-rule="required" class="form-control" name="row[account_type]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_number" data-rule="required" class="form-control" name="row[account_number]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refuse_reason')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refuse_reason" data-rule="required" class="form-control" name="row[refuse_reason]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fail_reason')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fail_reason" data-rule="required" class="form-control" name="row[fail_reason]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Memo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-memo" data-rule="required" class="form-control" name="row[memo]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key|htmlentities}"><input id="row[status]-{$key|htmlentities}" name="row[status]" type="radio" value="{$key|htmlentities}" {in name="key" value="3"}checked{/in} /> {$vo|htmlentities}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
