<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_sn')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_sn" data-rule="required" class="form-control" name="row[order_sn]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Package_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-package_id" data-rule="required" data-source="package/index" class="form-control selectpage" name="row[package_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Packagename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-packagename" data-rule="required" class="form-control" name="row[packagename]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Packagethumbimage')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-packagethumbimage" data-rule="required" class="form-control" step="0.01" size="50" name="row[packagethumbimage]" type="number" value="0.00">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-packagethumbimage" class="btn btn-danger faupload" data-input-id="c-packagethumbimage" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-packagethumbimage"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-packagethumbimage" class="btn btn-primary fachoose" data-input-id="c-packagethumbimage" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-packagethumbimage"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-packagethumbimage"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Storename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-storename" data-rule="required" class="form-control" name="row[storename]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Storelogo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-storelogo" data-rule="required" class="form-control" name="row[storelogo]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Limitday')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-limitday" data-rule="required" class="form-control" name="row[limitday]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_amount" data-rule="required" class="form-control" step="0.01" name="row[total_amount]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_fee" class="form-control" step="0.01" name="row[total_fee]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_fee" data-rule="required" class="form-control" step="0.01" name="row[pay_fee]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Transaction_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-transaction_id" data-rule="required" data-source="transaction/index" class="form-control selectpage" name="row[transaction_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[payment_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[payment_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_type" data-rule="required" class="form-control" name="row[pay_type]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paytime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-paytime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[paytime]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform" data-rule="required" class="form-control" name="row[platform]" type="text" value="wxMiniProgram">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ext')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-ext" class="form-control " rows="5" name="row[ext]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key|htmlentities}"><input id="row[status]-{$key|htmlentities}" name="row[status]" type="radio" value="{$key|htmlentities}" {in name="key" value="-2"}checked{/in} /> {$vo|htmlentities}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
