<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

  
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="xycourse/user/user/index" data-field="mobile" placeholder="输入手机号搜索" data-format-item="{id} - {mobile} - {nickname}" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coupon_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coupon_id" data-rule="required" data-source="xycourse/coupon/index" class="form-control selectpage" name="row[coupon_id]" type="text" value="">
        </div>
    </div>
   
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('赠送数量')}:</label>
        <div class="col-xs-12 col-sm-3">
            <input id="c-num" data-rule="required" class="form-control" name="row[num]" type="number" value="1">
        </div>

        <div class="col-xs-12 col-sm-5 lh">
            单次最多可赠送500个
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ishandsel')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-ishandsel" data-rule="required" class="form-control selectpicker" name="row[ishandsel]">
                {foreach name="ishandselList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="0"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>
        </div>
    </div>
    
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
    [v-clock] {display: none;}
    .lh{line-height: 34px;}
</style>