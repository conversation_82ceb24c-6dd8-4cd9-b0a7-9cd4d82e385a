<template>
	<view class="package-detail p-b-40" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		<view class="top">
			<swiper :circular="true" :autoplay="false" interval="5000" duration="500">
				<swiper-item v-for="(item, keys) in packageInfo.images" :key="keys" >
					<image :src="item" @tap="$xyfun.pi(keys,packageInfo.images)" />
					<view class="total ts-28 tc-w"><text>{{keys+1}}</text>/<text>{{packageInfo.images.length}}</text></view>
				</swiper-item>
			</swiper>
		</view>
		
		<view class="info-box m-30 p-tb-40 p-lr-30 br-20 bc-w">
			<view class="name-box flex">
				<view class="name m-r-20 ts-32 tb">{{packageInfo.name}}</view>
				
				<view class="r m-l-auto ts-34 " :style="css.tcl">
					<text class="xyicon ts-32 icon-share1" @tap="showShare = !showShare"></text>
				</view>
			</view>
			<view class="price-box m-t-30 flex">
				<view class="price ts-42 tb m-r-20" :style="css.tcp">¥{{packageInfo.price}}</view>
			</view>
			<view class="sales m-l-auto ts-28 m-t-20 flex" :style="css.tcl">
				<view class="item">已售 {{packageInfo.total_sales}}</view>
				<view class="m-l-auto">有效期：<text :style="css.tcm">{{packageInfo.limitday}}天</text></view>
			</view>
		</view>
		
		<view class="course-box m-30 p-tb-40 p-lr-30 br-10 bc-w">
			
			<view class="title tb ts-32 lh-32 flex"><view class="line m-r-10" :style="css.mcbg"></view>包含课程</view>
			<view class="course-list">
				<view class="m-t-30" v-for="item in packageInfo.courselist" :key="item.id">
					<xy-package-course :item="item" />
				</view>
			</view>
			
		</view>
		
		<view class="info-box m-30 p-tb-40 p-lr-30 br-10 lh-40 bc-w">
			<view class="title tb ts-32 flex lh-32"><view class="line m-r-10" :style="css.mcbg"></view>使用说明:</view>
			<view class="m-t-30">
				<rich-text :nodes="packageInfo.content"></rich-text>
			</view>
		</view>
		
		<view class="bottom-fixed flex tc bc-w">
			<view class="p-lr-30 p-tb-15 flex tc wa">
				<button class="home m-r-40 tc lh-32" @tap="$xyfun.to('/pages/index',true)" :style="css.tcl">
					<text class="xyicon icon-home ts-36 lh-36"></text>
					<view class="text ts-26 m-t-10 ts-26">首页</view>
				</button>
				<button class="kefu m-r-40 tc lh-32" :style="css.tcl" @tap="$xyfun.phone(packageInfo.storeInfo.phone)">
					<text class="xyicon icon-kefu ts-36 lh-36"></text>
					<view class="text ts-26 m-t-10 ts-26">客服</view>
				</button>
				<view class="action flex m-l-auto">
					<view class="buy m-l-auto tc-w" :style="css.mcbg" @tap="buyCourse()">
						立即购买
					</view>
				</view>
			</view>
		</view>
		
		<!--购买弹窗-->
		<block v-if="buyModelShow">
			<view class="xy-modal-box bottom-fixed xy-modal-box-bottom buy-model-box ovh" :style="css.pbg" :class="[buyModelShow?'xy-modal-show':'']">
				<view class="title p-tb-35 tb tc" :style="css.mbg">课程包订单</view>
				<view class="item buy-package p-30 m-b-2 bc-w">
					<xy-package-row :item="packageInfo" :buy="false" />
				</view>
				<view class="item flex p-30 m-b-2 lh-50 bc-w">
					<view class="l">上课门店：{{packageInfo.storeInfo.name}}</view>
				</view>
				
				<view class="item flex p-30 m-b-2 lh-50 bc-w m-t-20">
					<view class="l">优惠券：</view>
					<view class="m-l-auto" @tap="$xyfun.to('/pages/user/coupon/list')" :style="css.tcp">抵扣¥{{couponMoney}}元 <text class="xyicon icon-right"></text></view>
				</view>
				
				<view class="pay-list m-t-20">
					<view class="item flex p-30 m-b-2 lh-50 bc-w" v-for="(item, index) in payList" :key="index" v-if="item.state">
						<view class="l flex">
							<text :class="'xyicon icon-'+item.icon+' ts-40 m-r-15 lh-40 p-t-5'"></text>
							<text class="lh-50">{{item.name}}</text>
						</view>
						<view v-if="item.method == 'balance'" :style="css.tcl" class="m-l-30">
							剩余{{user.info.money}}元可用
						</view>
						<view class="r tb m-l-auto" @tap="payMethodSelect(index)">
							<text class="xyicon icon-radio-a ts-32 lh-50" v-if="item.select"></text>
							<text class="xyicon icon-radio ts-32 lh-50" v-else></text>
						</view>
					</view>
				</view>
				
				<view class="item flex p-30 m-t-20 m-b-2 lh-50 bc-w">
					<view class="agree flex " @tap="isAgree=!isAgree">
						<text class="xyicon icon-radio-a ts-30 flex tb" v-if="isAgree"></text>
						<text class="xyicon icon-radio ts-30 flex tb" v-else></text>
						<text class="m-l-10">同意</text>
					</view>
					<text class="tc-blue" @tap="see">《购买课程包协议》</text>
				</view>
				<view class="item flex p-30" :style="css.mbg">
					<view class="l flex p-t-25 lh-50">
						<view :style="css.tcp" class="ts-42 tb"><text class="ts-26">¥</text>{{totalMoney}}</view>
					</view>
					<view class="r m-l-auto confirm tc tc-w" :style="css.mcbg" @tap="createOrder">确认支付</view>
				</view>
			</view>
			<view class="xy-modal-mask" :class="[buyModelShow?'xy-mask-show':'']" @tap="buyModelShow =!buyModelShow"></view>
		</block>
		
		<!--分享组件 -->
		<xy-share v-model="showShare" posterType="package" :posterInfo="packageInfo" />
		
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import xyShare from '@/components/xy-share';
	import xyPackageCourse from '@/components/xy-package/course';
	import xyPackageRow from '@/components/xy-package/row';
	import share from '@/utils/share';
	import Pay from '@/utils/pay';
	export default {
		components: {
			xyShare,
			xyPackageCourse,
			xyPackageRow
		},
		data() {
			return {
				css:{},
				id:0,
				isLoading:true,
				packageInfo:{},
				buyModelShow:false,
				isAgree:false,
				showShare:false,
				couponMoney:0,
				totalMoney:0,
				userCoupon:null,
				payList:[{
					name: '余额支付',
					method: 'balance',
					icon: 'balance',
					state: true,
					select: false
				},
				{
					name: '微信支付',
					method: 'wechat',
					icon: 'wechat',
					state: true,
					select: true
				}]
			}
		},
		computed: {
			...mapState(['common','user'])
		},
		onLoad() {
			
			var options = this.$Route.query;
			
			// #ifdef MP
			if (options?.scene) {
				options = this.$xyfun.sceneDecode(options.scene);
			}
			// #endif
			
			if(options?.id){
				this.id = options.id;
			}
			
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		onUnload() {
			share.setShareInfo();
		},
		methods: {
			
			loadData(){
				this.$api.get({
					url: '/package/detail',
					loadingTip:'加载中...',
					data: {
						id:this.id,
					},
					success: res => {
						this.packageInfo = res;
						this.isLoading = false;
						this.$xyfun.setNavTitle(this.packageInfo.name);
						
						share.setShareInfo({
							title: res.name,
							image: res.images[0],
							params: {
								page: 2,
								pageId: this.id
							}
						});
					}
				});
				
			},
		
			//立即购买
			buyCourse(){
				if(this.user.isLogin){
					this.$api.post({
						url: '/order/init',
						data: {
							package_id:this.packageInfo.id,
						},
						success: res => {
							this.totalMoney = res.totalMoney;
							this.couponMoney = res.couponMoney;
							this.userCoupon = res.userCoupon;
							this.buyModelShow = true;
						},
						fail: res => {
							console.log(res);
						}
					});
					
				}else{
					this.$xyfun.toLogin();
				}
			},
			
			//创建订单
			createOrder(){
				if(this.isAgree){
					if (this.disabled) {
						return;
					}
					this.disabled = true;
					var pay_type = '';
					this.payList.map((value) => {
					　　if(value.select){
							pay_type = value.method;
						}
					});
					if (!pay_type) {
						this.$xyfun.msg('请选择支付方式');
					}else{
						
						// 提交订单
						this.$api.post({
							url: '/order/add',
							data: {
								package_id:this.packageInfo.id,
								pay_type:pay_type,
								user_coupon_id:this.userCoupon?this.userCoupon.id:0
							},
							loadingTip: '提交订单中...',
							success: res => {
								//发起支付
								var pay = new Pay(pay_type, res, 'order');
								pay.payMehod().then((res)=>{
									this.disabled = false;
									pay.payResult(res);
								},(res)=>{
									this.disabled = false;
									if(pay_type != 'balance'){
										pay.payResult(res);
									}
								});
							},
							fail: res => {
								this.disabled = false;
							}
						});
						
					}
				}else{
					this.$xyfun.msg('请阅读协议，并勾选同意按钮');
				}
			},
			
			//支付方式选择
			payMethodSelect(key){
				
				this.payList.map((value,index) => {
				　　if(index == key){
						value.select = !value.select;
					}else{
						value.select = false;
					}
				});
			},
			
			see(){
				if(this.common.appConfig.courseagree > 0){
					this.$xyfun.to('/pages/article/detail?id='+this.common.appConfig.courseagree);
				}
			}
			
		}
	}
</script>

<style scoped lang="scss">
	.package-detail{
		padding-bottom: 150rpx;
		.top,.top swiper{
			width: 750rpx;height: 400rpx;position: relative;position: relative;
			image{
				width: 750rpx;height: 400rpx;
			}
			.total{
				background: rgba(0, 0, 0, 0.5);
				position: absolute; right: 30rpx; bottom: 30rpx;
				padding: 3rpx 15rpx;border-radius: 20rpx;
				text{margin: 0 10rpx;}
			}
		}
		.line{width: 6rpx;border-radius: 5rpx;height: 30rpx;}
		.info-box{
			.name-box{
				line-height: 34rpx;
				.score{
					padding: 3rpx 8rpx;
				}
			}
			
			.sales{
				.item{
					width: 33.3%;
				}
			}
		}
		
		
		
		.bottom-fixed{
			button{background: none;padding: 0;margin-left: 0;}
			button::after{border: none;}
			.buy{width: 260rpx;height: 72rpx;border-radius: 36rpx;line-height: 72rpx;}
		}
		
		.buy-model-box{
			.buy-package{
				.l{
					image{width: 110rpx;height: 110rpx;}
				}
			}
			.sales{
				.r view{height: 58rpx;width: 58rpx;}
				.r input{width: 100rpx;height: 60rpx;}
			}
			.confirm{width: 240rpx;height: 80rpx;border-radius: 40rpx;line-height: 80rpx;}
		}
	}
</style>