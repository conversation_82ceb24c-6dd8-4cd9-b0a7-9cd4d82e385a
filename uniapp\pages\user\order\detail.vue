<template>
	<view class="order-detail p-b-40" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isEmpty">
		<view class="top psr" :style="css.mcbg">
			<image class="bg" src="/static/images/status-bg.png" mode="widthFix" />
			<view class="status-left flex">
				<image :src="'/static/images/order-status'+order.status+'.png'" mode="widthFix" v-if="order.status >=0" />
				<image src="/static/images/order-status-close.png" mode="widthFix" v-else />
				<view class="status-box m-l-20 tc-w">
					<view :class="'name tb ts-34 ' + (order.status == -2 ? 'p-t-10':'p-t-40')">
						{{order.status_text}}
					</view>
					<view class="tips ts-28 lh-28 m-t-10 tc-w" v-if="order.status == -2">长时间未支付，订单自动关闭</view>
				</view>
			</view>
			<view class="status-right ts-28 tc-w" v-if="order.status ==0">
				<view class="flex">
					<text>剩余时间：</text>
					<view class="time">{{time}}</view>
				</view>
			</view>
		</view>
		
		<view v-if="type == 'order'" class="flex p-lr-30 m-30 br-10 bc-w">
			<view class="item-list flex ">
				<view class="item flex p-tb-30" :key="index" @tap="$xyfun.to('/pages/package/detail?id='+order.package_id)">
					<view class="l"><image :src="order.packagethumbimage" class="br-10" /></view>
					<view class="r m-l-20">
						<view class="m-t-10 tb">
							{{order.packagename}}
						</view>
						<view class="ts-36 tb m-t-15" :style="css.tcp">
							<text class="ts-28">¥</text>{{order.total_amount}}
						</view>
						<view class="flex m-t-15 lh-54">
							<view class="ts-28" :style="css.tcl">有效期：<text :style="css.tcm">{{order.limitday}}</text>天</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="m-t-30 br-10 m-lr-30 m-b-30 p-30 bc-w">
			<view class="flex">
				<text>订单编号：</text>
				<text>{{order.order_sn}}</text>
				<text @tap="$xyfun.copy(order.order_sn)" class="ts-24 bl br-10 m-l-25 p-lr-10" :style="css.pbg">复制</text>
			</view>
			<view class="flex m-t-20">
				<text>下单时间：</text>
				<text>{{$xyfun.timeFormat(order.createtime)}}</text>
			</view>
		</view>
		<view class="m-t-30 br-10 m-lr-30 m-b-30 p-30 bc-w">
			
			<view class="flex">
				<text>订单金额：</text>
				<view class="m-l-auto tb"><text>¥</text>{{order.total_amount}}</view>
			</view>
			
			<view class="flex m-t-20">
				<text>优惠金额：</text>
				<view class="m-l-auto tb"><text>¥</text>{{order.coupon_fee}}</view>
			</view>
			<view class="flex m-t-20">
				<text>需付金额：</text>
				<view class="m-l-auto tb"><text>¥</text>{{order.total_fee}}</view>
			</view>
				
			<view class="flex m-t-20">
				<view class="m-l-auto tb lh-34">
					<text>实付金额：</text>
					<text :style="css.tcp">¥</text>
					<text class="ts-34" :style="css.tcp">{{order.pay_fee ? order.pay_fee : order.total_fee}}</text>
				</view>
			</view>
		</view>
		<view class="bottom-fixed flex tc bc-w" v-if="order.status == 0">
			<view class="p-lr-30 p-tb-15 flex tc wa">
				<view class="action flex m-l-auto">
					<view class="close m-r-40" :style="css.pbg+css.tcl" @tap="closeOrder()">取消订单</view>
					<view class="pay m-l-auto tc-w" :style="css.mcbg" @tap="onPay()">支付</view>
				</view>
			</view>
		</view>
		
		<!--支付弹窗-->
		<block v-if="payModelShow">
			<view class="xy-modal-box xy-modal-box-center pay-model-box p-t-30 br-10 bc-w" :class="[payModelShow?'xy-modal-show':'']">
				<view class="title m-b-50 ts-32 tc bl-b p-b-30" :style="css.blpc">支付方式</view>
				<view class="tc tb m-tb-40 ts-36">支付金额{{ order.total_fee || "0.00" }}元</view>
				<view class="pay-list">
					<view class="item flex p-30 m-b-2 lh-40 bc-w" v-for="(item, index) in payList" :key="index" v-if="item.state">
						<view class="l flex">
							<text :class="'xyicon icon-'+item.icon+' ts-40 m-r-15'"></text>
							<text class="lh-40">{{item.name}}</text>
						</view>
						<view class="r tb m-l-auto" @tap="payMethodSelect(index)">
							<text class="xyicon icon-radio-a ts-32 lh-40" v-if="item.select"></text>
							<text class="xyicon icon-radio ts-32 lh-40" v-else></text>
						</view>
					</view>
				</view>
				
				<button :style="css.mcbg" class="ts-30 lh-30 p-25 tc-w m-30" @tap="subPay()">确认支付</button>
			</view>
			<view class="xy-modal-mask" :class="[payModelShow?'xy-mask-show':'']" @tap="payModelShow =!payModelShow"></view>
		</block>
		
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import Pay from '@/utils/pay';
	export default {
		data() {
			return {
				css:{},
				isEmpty:true,
				id: 0,
				type:'goods',
				order:{},
				time:'',
				timer:null,
				payModelShow:false,
				disabled:false,
				payList:[{
					name: '余额支付',
					method: 'balance',
					icon: 'balance',
					state: true,
					select: false
				},
				{
					name: '微信支付',
					method: 'wechat',
					icon: 'wechat',
					state: true,
					select: true
				}]
			}
		},
		computed: {
			...mapState(['common','user'])
		},
		async onLoad() {
			var options = this.$Route.query;
			if(options){
				this.id = options.id;
				this.type = options.type;
			}
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		methods: {
			
			async loadData(){
				var url = '';
				switch (this.type) {
					case 'order':
						url = '/order/detail';
						break;
					default:
						break;
				}
				if(url != ''){
					this.$api.post({
						url: url,
						loadingTip:'加载中...',
						data: {
							id: this.id,
						},
						success: res => {
							this.isEmpty = false;
							this.order = res;
							if(res.status == 0){
								this.timer=setInterval(()=>{
									this.countDown(res.ext_arr.expired_time);
								},1000)
							}
						}
					});
				}
			},
			
			//倒计时
			countDown(endtime){
				if(this.$xyfun.intervalTime(endtime) == '00:00:00'){
					clearInterval(this.timer)
				}
				this.time = this.$xyfun.intervalTime(endtime);
			},
			
			//关闭订单
			closeOrder(){
				var url = '';
				switch (this.type) {
					case 'order':
						url = '/order/cancel'
						break;
					default:
						break;
				}
				if(url != ''){
					this.$api.post({
						url: url,
						data: {
							id: this.id,
						},
						success: res => {
							this.loadData();
						}
					});
				}
			},
			
			// 重新支付
			onPay() {
				this.payModelShow = true;
			},
			
			//支付方式选择
			payMethodSelect(key){
				
				this.payList.map((value,index) => {
				　　if(index == key){
						value.select = !value.select;
					}else{
						value.select = false;
					}
				});
			},
			
			//确认支付
			subPay(){
				
				if(this.disabled){
					return false;
				}
				this.disabled = true;
				
				var pay_type = '';
				this.payList.map((value) => {
				　　if(value.select){
						pay_type = value.method;
					}
				});
				if (!pay_type) {
					this.$xyfun.msg('请选择支付方式');
				}else{
					//发起支付
					var pay = new Pay(pay_type, this.order, this.type);
					pay.payMehod().then((res)=>{
						this.disabled = true;
						pay.payResult(res);
					},()=>{
						this.disabled = false;
					});
				}
			},
			
		}
	}
</script>

<style scoped lang="scss">
	.order-detail{
		padding-bottom: 150rpx;
		.top{
			.bg{
				width: 750rpx;
			}
			
			.status-left{
				position: absolute;left: 50rpx; top: 40rpx;
				image{width: 120rpx;}
			}
			
			.status-right{
				position: absolute;right: 30rpx;top: 80rpx;
			}
		}
		
		.time{width: 115rpx;}
		
		.bottom-fixed{
			.close{width: 220rpx;height: 74rpx;border-radius: 37rpx;line-height: 74rpx;}
			.pay{width: 180rpx;height: 74rpx;border-radius: 37rpx;line-height: 74rpx;}
		}
		
		.logistics-model-box{
			width: 660rpx;
		}
		
		.pay-model-box{width: 80%;}
		
	}
	
	.item-list{
		.item{
			.l,.l image{
				width: 240rpx;height: 180rpx;
			}
			.r{
				width: 370rpx;
				.price-box{position: absolute; right: 0;top: 0;}
			}
		}
	}
</style>