<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('会员')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-source="xycourse/user/user/index" data-field="mobile" placeholder="输入手机号搜索" data-format-item="{id} - {mobile} - {nickname}" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('课程')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_id" data-rule="required" data-source="xycourse/course/index" class="form-control selectpage" name="row[course_id]" type="text" value="">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-num" data-rule="required" class="form-control" name="row[num]" type="number" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Residue')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-residue" data-rule="required" class="form-control" name="row[residue]" type="number" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Duedate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-duedate" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[duedate]" type="text" value="">
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
