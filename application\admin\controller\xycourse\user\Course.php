<?php

namespace app\admin\controller\xycourse\user;

use app\common\controller\Backend;
use Exception;
use think\exception\PDOException;
use app\admin\model\xycourse\Course as CourseModel;
use app\api\model\xycourse\user\Course as UserCourseModel;
use app\admin\model\xycourse\package\Package as PackageModel;
use app\admin\model\xycourse\package\Item as ItemModel;
use app\admin\model\xycourse\user\Package as UserPackageModel;

use think\Db;
use think\exception\DbException;
use think\exception\ValidateException;
use think\response\Json;

/**
 * 用户课程
 *
 * @icon fa fa-circle-o
 */
class Course extends Backend
{

    /**
     * Course模型对象
     * @var \app\admin\model\xycourse\user\Course
     */
    protected $model = null;
    protected $searchFields = 'id,user.nickname,user.mobile,name';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xycourse\user\Course;
        $this->view->assign("typeList", $this->model->getTypeList());
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }

        $course = CourseModel::get($params['course_id']);

        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
    
            $params['store_id'] = 1;
            $params['name'] = $course['name'];
            $params['type'] = $course['type'];
            $params['thumbimage'] = $course['thumbimage'];
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }



    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function package()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }

        $package =  PackageModel::get($params['package_id']);



        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $duedate = $duedate = date('Y-m-d', strtotime("+".$package['limitday']." day"));
            
            //增加用户课程包
            $userPackage = UserPackageModel::create([
                'user_id'            => $params['user_id'],
                'store_id'            => 1,
                'package_id'         => $params['package_id'],
                'packagename'        => $package['name'],
                'packagethumbimage'  => $package['thumbimage'],
                'duedate'            => $duedate,
            ]);

            //更改用户课程增加记录
            $courseList = ItemModel::where(['package_id'=>$params['package_id']])->select();
            foreach($courseList as $cl){
                UserCourseModel::change($userPackage->id,$cl['course_id'],$params['user_id'],$cl['num'],'sys_buy',0,$duedate);
            }

            $result = true;

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }


    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index()
    {
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();
        $list = $this->model
            ->with(['store','user'])
            ->where($where)
            ->order($sort, $order)
            ->paginate($limit);

        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }

}
