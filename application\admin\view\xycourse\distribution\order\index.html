<div class="panel panel-default panel-intro">
    
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key|htmlentities}" data-value="{$key|htmlentities}" data-toggle="tab">{$vo|htmlentities}</a></li>
            {/foreach}
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('xykeep.course.order/edit')}"
                           data-operate-del="{:$auth->check('xykeep.course.order/del')}"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>

    
    <script type="text/html" id="itemtpl">
        <% if(i == 0){ %>
            <div class="order-head">
                <div class="col-sm-1 text-center obr">订单类型</div>
                <div class="col-sm-3 flex obr">购买详情</div>
                <div class="col-sm-1 flex obr">订单金额</div>
                <div class="col-sm-2 obr">买家信息</div>
                <div class="col-sm-2 obr">一级分佣</div>
                <div class="col-sm-2 obr">二级分佣</div>
                <div class="col-sm-1 text-center obr">佣金状态</div>
            </div>
        <% } %>
    
        <div class="col-sm-12 mt-15">
            <div class="item-top">
                <span>订单号：<%=item.order_sn%></span>
                <span style="margin-left:100px;">下单时间：<%=Moment(item.createtime*1000).format("YYYY-MM-DD HH:mm:ss")%></span>
                <span style="margin-left:100px;">结算时间：<%=Moment(item.settle_time*1000).format("YYYY-MM-DD HH:mm:ss")%></span>
            </div>
            <div class="item-content">
                
                <div class="col-sm-1 vhc obr flex">
                    <%=item.order_type_text%>
                </div>

                <div class="col-sm-3 vhc obr flex" style="padding: 15px;">

                    <% if(item.order_type == 'order'){ %>
                        <div class="order_img ">
                            <a href="javascript:;"><img class="img-md img-center" src="<%=cdnurl(item.order_info.packagethumbimage)%>" alt="<%=item.order_info.packagename%>"></a>
                        </div>
                        <div style="margin-left: 10px;">
                            <%=item.order_info.packagename%>
                        </div>
                    <% } %>

                </div>

                <div class="col-sm-1 vhc obr flex">
                    <%=item.total_fee%>
                </div>
                <div class="col-sm-2 vhc obr flex">

                    <div class="col-sm-12" style="padding: 0;">
                        <%=item.buyer.nickname%>(ID:<%=item.buyer.id%>)<br/>
                        <%=item.buyer.mobile%>
                    </div>

                </div>

                <div class="col-sm-2 vhc obr flex">
                    <% if(item.one_distribution_id == 0){ %>
                        无
                    <% }else{ %>
                        <div class="col-sm-12" style="padding: 0;">
                            <%=item.one.nickname%>(ID:<%=item.one.id%>)<br/>
                            <%=item.one.mobile%><br/>
                            佣金：<samp class="text-red">￥<%=item.one_commission%></samp>
                        </div>
                    <% } %>
                    
                </div>

                <div class="col-sm-2 vhc obr flex">
                    <% if(item.two_distribution_id == 0){ %>
                        无
                    <% }else{ %>
                        <div class="col-sm-12" style="padding: 0;">
                            <%=item.two.nickname%>(ID:<%=item.two.id%>)<br/>
                            <%=item.two.mobile%><br/>
                            佣金：<samp class="text-red">￥<%=item.two_commission%></samp>
                        </div>
                    <% } %>
                </div>

                <div class="col-sm-1 vhc obr flex">
                    <%=item.status_text%>
                </div>
                
            </div>
        </div>
    </script>
    
    <style>
        .flex{display: flex;flex-wrap: wrap;}
        .mt-15{margin-top: 15px;}
        .ctr{text-align: right;line-height: 35px;font-weight: bold;}
        .vhc{padding: 15px;height: 100%;align-items: center;justify-content: center;}
        .order-head{background: #FFFCF7;padding: 0px 15px;width: 100%;font-weight: bold;margin-top: 5px;}
        .order-head div{padding: 15px;}
        .item-top{background: #FFFCF7;padding: 10px;width: 100%;border: 1px solid #e6e6e6;}
        .item-content{border: 1px solid #e6e6e6;border-top: none;}
        .obr{border-right: 1px solid #e6e6e6;}
        .item-goods{margin-bottom: 10px;}
        .item-goods:last-child{margin-bottom: 0;}
    </style>
</div>

