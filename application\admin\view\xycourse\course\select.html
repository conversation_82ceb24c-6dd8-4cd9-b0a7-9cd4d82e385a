<style>
    #one .commonsearch-table{
        padding-top:15px!important;
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="type">
            <li class="{:$Think.get.type === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="typeList" item="vo"}
            <li class="{:$Think.get.type === (string)$key ? 'active' : ''}"><a href="#t-{$key|htmlentities}" data-value="{$key|htmlentities}" data-toggle="tab">{$vo|htmlentities}</a></li>
            {/foreach}
        </ul>
    </div>


    <div class="panel-body no-padding">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh')}
                        {if request()->get('multiple') == 'true'}
                        <a class="btn btn-danger btn-choose-multi"><i class="fa fa-check"></i> {:__('Choose')}</a>
                        {/if}
                    </div>
                    <table id="table" class="table table-bordered table-hover table-nowrap" width="100%">

                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
