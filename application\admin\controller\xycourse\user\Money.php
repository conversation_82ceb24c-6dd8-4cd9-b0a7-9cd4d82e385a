<?php

namespace app\admin\controller\xycourse\user;

use app\common\controller\Backend;

/**
 * 会员余额明细管理
 *
 * @icon fa fa-circle-o
 */
class Money extends Backend
{

    /**
     * Money模型对象
     * @var \app\admin\model\xycourse\user\Money
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xycourse\user\Money;

    }

    /**
     * 查看
     */
    public function index($user_id = '')
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            if($user_id){
                $list = $this->model
                ->with(['user'])
                ->where($where)
                ->where('user_id',$user_id)
                ->order($sort, $order)
                ->paginate($limit);
            }else{
                $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);
            }    

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        $this->assignconfig('user_id',$user_id);
        return $this->view->fetch();
    }

    //添加
    public function add() {
        return;
    }
    //编辑
    public function edit($ids = null) {
        return;
    }
    //删除
    public function del($ids = null) {
        return;
    }
    //回收站列表
    public function recyclebin() {
        return;
    }
    //回收站(真实删除或清空)
    public function destroy($ids = null) {
        return;
    }
    //回收站还原
    public function restore($ids = null) {
        return;
    }
    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }

}
