<?php

namespace addons\xycourse\listener;
use app\api\model\xycourse\order\Order as OrderModel;
use app\api\model\xycourse\order\Item as ItemModel;
use app\api\model\xycourse\recharge\Order as RechargeModel;
use app\api\model\xycourse\user\Course as UserCourseModel;
use app\api\model\xycourse\user\Package as UserPackageModel;
use app\api\model\xycourse\user\Coupon as UserCouponModel;
use app\api\model\xycourse\package\Package as PackageModel;
use app\api\model\xycourse\user\User;
use think\Log;

/**
 * 订单钩子
 */
class Order
{

    // 订单支付成功 
    public function xycourseOrderPayedAfter(&$params){

        $orderData = $params['order'];
        $orderType = $params['type'];  // 订单类型

        // 重新查询订单
        $order = null;
       
        // 课程包订单
        if ($orderType == 'order') {
            $order = OrderModel::where(['id'=>$orderData['id']])->find();
        }

        // 充值订单
        if ($orderType == 'recharge') {
            $order = RechargeModel::where(['id'=>$orderData['id']])->find();
        }

        if (!$order) return false;
        
        $user = User::get($order->user_id);

        if(empty($user)) return false;

        //充值订单
        if($orderType == 'recharge'){
            $user->setInc('xycourse_recharge',$order['money']);
        }

        // 课程包订单
        if ($orderType == 'order') {

            // 增加用户消费金额
            $user->setInc('xycourse_consume',$order->total_fee);

            // 更新优惠券状态
            if($order['user_coupon_id'] > 0){
                $userCoupon  = UserCouponModel::get($order['user_coupon_id']);
                $userCoupon->status = 1;
                $userCoupon->useorderid = $order['id'];
                $userCoupon->useordertype = $orderType;
                $userCoupon->save();
            }
            
            //增加用户课程包
            $duedate = date('Y-m-d', strtotime("+".$order['limitday']." day"));
            $userPackage = UserPackageModel::create([
                'user_id'            => $order['user_id'],
                'store_id'           => $order['store_id'],
                'package_id'         => $order['package_id'],
                'packagename'        => $order['packagename'],
                'packagethumbimage'  => $order['packagethumbimage'],
                'duedate'            => $duedate,
            ]);

            //更改用户课程增加记录
            $courseList = ItemModel::where(['order_id'=>$order->id])->select();
            foreach($courseList as $cl){
                UserCourseModel::change($userPackage->id,$cl['course_id'],$order['user_id'],$cl['num'],'order',$order['user_id'],$duedate);
            }

            //更新课程包销量
            $package = PackageModel::get($order['package_id']);
            if(!empty($package)){
                $package->setInc('sales');
            }

        }

        
    }
    
}
