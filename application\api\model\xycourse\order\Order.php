<?php

namespace app\api\model\xycourse\order;
use app\api\model\xycourse\user\User;
use app\api\model\xycourse\order\Item as OrderItem;
use app\api\model\xycourse\package\Package as PackageModel;
use app\api\model\xycourse\user\Coupon as UserCouponModel;
use addons\xycourse\exception\Exception;
use think\Model;
use think\Db;
use think\Queue;

class Order extends Model
{

    // 表名
    protected $name = 'xycourse_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'paytime_text',
        'status_text',
        'ext_arr'
    ];

    public function getExtArrAttr($value, $data)
    {
        $ext = (isset($data['ext']) && $data['ext']) ? json_decode($data['ext'], true) : [];
        return $ext;
    }

    //订单支付加载数据
    public static function getInitData($params){

        $user = User::info();
        extract($params);
        $order = self::with(['item'])->where(['user_id'=>$user->id,'id'=>$order_id])->find();
        if (!$order) {
            new Exception('订单不存在');
        }
        if($order['status'] != 0){
            new Exception('订单状态错误');
        }
        return $order;
    }

    public function setExt($order, $field, $origin = [])
    {
        $newExt = array_merge($origin, $field);

        $orderExt = $order['ext_arr'];

        return array_merge($orderExt, $newExt);
    }

    // 订单详情
    public static function getDetail($params)
    {
        $user = User::info();
        extract($params);

        $order = self::with(['item'])->where('user_id', $user->id);

        if (isset($order_sn)) {
            $order = $order->where('order_sn', $order_sn);
        }
        if (isset($id)) {
            $order = $order->where('id', $id);
        }

        $order = $order->find();

        if (!$order) {
            new Exception('订单不存在');
        }

        return $order;
    }

    // 取消订单
    public static function cancelOrder($params)
    {
        $user = User::info();
        extract($params);
        $order = self::where(['user_id'=>$user->id,'id'=>$id,'status'=>0])->find($id);
        if (!$order) {
            new Exception('订单不存在或已取消');
        }
        $order->status = -1;        // 取消订单
        $order->ext = json_encode($order->setExt($order, ['cancel_time' => time()]));      // 取消时间
        $order->save();
        
        return $order;
    }

    /**
     * 订单加载
     */
    public static function orderInit($params){
        $user = User::info();
        extract($params);

        $totalMoney  = 0;
        $couponMoney = 0;

        // 课程包
        $packageInfo = PackageModel::with(['item.course'])->where(['id'=>$package_id])->find();

        // 查询会员可用优惠券
        $userCoupon = UserCouponModel::where(['user_id'=>$user->id,'status'=>0,'endtime'=>['>',time()],'atleast'=>['<=',$packageInfo['price']]])->order('money desc,discount desc')->find(); 

        if(!empty($userCoupon)){
            $totalMoney = bcsub($packageInfo['price'],$userCoupon['money'],2);
            $couponMoney = $userCoupon['money'];
        }else{
            $totalMoney = $packageInfo['price'];
        }

        return ['totalMoney'=>$totalMoney,'couponMoney'=>$couponMoney,'userCoupon'=>$userCoupon];
    }


    /**
     * 创建订单
     */
    public static function addOrder($params)
    {
        $user = User::info();
        extract($params);

        // 课程包
        $packageInfo = PackageModel::with(['item.course'])->where(['id'=>$package_id])->find();
        if(empty($packageInfo)){
            new Exception('课程包不存在！');
        }

        //优惠券
        $userCoupon = null;
        if($user_coupon_id > 0){
            $userCoupon = UserCouponModel::get($user_coupon_id);
        }

        $close_time = 15;
        $order = Db::transaction(function () use ($user,$packageInfo,$userCoupon,$close_time) {
            

            $couponFee = $userCoupon?$userCoupon['money']:0;

            $orderData = [];
            $orderData['order_sn'] = xycourseCreateOrderNo();
            $orderData['user_id'] = $user->id;
            $orderData['package_id'] = $packageInfo->id;
            $orderData['packagename'] = $packageInfo->name;
            $orderData['packagethumbimage'] = $packageInfo->thumbimage;
            $orderData['limitday'] = $packageInfo->limitday;
            $orderData['total_amount'] = $packageInfo->price;
            $orderData['coupon_fee'] = $couponFee;
            $orderData['user_coupon_id'] = $userCoupon ? $userCoupon['id'] : 0;
            $orderData['total_fee'] = bcsub($packageInfo->price,$couponFee,2);
            $orderData['ext'] = json_encode(['expired_time' => time() + ($close_time * 60)]);
            $order = new Order();
            $order->allowField(true)->save($orderData);

            // 添加订单选项
            foreach ($packageInfo['item'] as $buyinfo) {
                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;
                $orderItem->course_id = $buyinfo['course']['id'];
                $orderItem->coursename = $buyinfo['course']['name'];
                $orderItem->coursethumbimage = $buyinfo['course']['thumbimage'];
                $orderItem->courseprice = $buyinfo['course']['price'];
                $orderItem->coursetype = $buyinfo['course']['type'];
                $orderItem->num = $buyinfo['num'];
                $orderItem->save();
            }

            return $order;
        });

        //订单关闭队列
        Queue::later(($close_time * 60), '\addons\xycourse\service\QueueOrder@autoClose', ['order' => $order,'type'=>'order'], 'XYcourse');

        return $order;
    }

    public static function getLists($params)
    {
        extract($params);
        $user = User::info();
        $where = ['user_id'=>$user->id];
        if($status != 'all'){
            $where['status'] = $status;
        }
        $list = self::with(['item'])->where($where)->order('id desc')->paginate();

        return $list;
    }


    /**
     * 订单支付成功
     *
     * @param [type] $order
     * @param [type] $notify
     * @return void
     */
    public function paySuccess($order, $notify)
    {

        $order->status = 1;
        $order->paytime = time();
        $order->transaction_id = $notify['transaction_id'];
        $order->payment_json = $notify['payment_json'];
        $order->pay_type = $notify['pay_type'];
        $order->pay_fee = $notify['pay_fee'];
        $order->save();

        // 支付后监听
        $params = ['order'=>$order,'type'=>'order'];
        \think\Hook::listen('xycourse_order_payed_after', $params);
        
        return $order;
    }

    
    public function getStatusList()
    {
        return ['-3' => __('已退订'),'-2' => __('已关闭'), '-1' => __('已取消'), '0' => __('待支付'), '1' => __('已支付')];
    }


    public function getPaytimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['paytime']) ? $data['paytime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setPaytimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function item()
    {
        return $this->hasMany(OrderItem::class, 'order_id', 'id');
    }


}
