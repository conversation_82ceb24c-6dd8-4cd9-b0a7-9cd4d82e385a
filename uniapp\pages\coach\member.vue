<template>
	<view class="appointment" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		<view class="xy-user-card" :style="css.mcbg">
			<view class="user-info flex br-20 bc-w p-30">
				<view class="l m-r-15">
					<image :src="$xyfun.image(memberInfo.avatar)" />
				</view>
				<view class="c">
					<view>
						<view class="ts-30 flex lh-32 p-t-20 tb" :style="css.tcm">
							<text>
								{{memberInfo.nickname}}
							</text>
						</view>
						<view class="ts-28 lh-28 m-t-15" :style="css.tcl">{{memberInfo.mobile}}</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="menu m-30">
			<view class="item bc-w br-20 p-30 flex m-b-30 lh-54">
				<view>预约记录</view>
				<view class="btn m-l-auto ts-26 tc-w p-lr-30" :style="css.mcbg" @tap="$xyfun.to('/pages/coach/member/appointment?user_id='+memberInfo.id)">查看</view>
			</view>
			
			<view class="item bc-w br-20 p-30 flex lh-54" @tap="$xyfun.to('/pages/coach/member/course?user_id='+memberInfo.id)">
				<view>会员课程</view>
				<view class="btn m-l-auto ts-26 tc-w p-lr-30" :style="css.mcbg">查看</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import DateTabs from '@/uni_modules/hope-11-date-tabs/components/hope-11-date-tabs/date-tabs.vue'
	import xyEmpty from '@/components/xy-empty';
	export default {
		components: {
			DateTabs,
			xyEmpty
		},
		data() {
			return {
				css:{},
				memberInfo:{},
				isLoading:true,
			}
		},
		computed: {
			...mapState(['common'])
		},
		onLoad() {
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		onPullDownRefresh() {
			this.loadData();
		},
		methods: {
			loadData(){
				this.$api.post({
					url: '/coach_center/member',
					loadingTip:'加载中...',
					data: {
						user_id: this.$Route.query.user_id,
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.isLoading = false;
						this.memberInfo = res;
					}
				});
			},
			
		}
	}
</script>

<style scoped lang="scss">
	
	.xy-user-card{
		height: 120rpx;
		.user-info{
			position: absolute;
			left: 30rpx;
			top: 30rpx;
			width: 690rpx;
			.l{
				image{width: 110rpx;height: 110rpx;border-radius: 55rpx;}
			}
			.c{
				width: 300rpx;
			}
		}
		
	}
	
	.menu{
		margin-top: 100rpx;
		.item{
			.btn{height: 54rpx;border-radius: 27rpx;}
		}
	}
	
</style>