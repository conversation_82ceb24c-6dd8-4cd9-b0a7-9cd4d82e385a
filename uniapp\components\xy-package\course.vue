<template>
	<view class="xy-package-course">
		<view class="item flex" :key="index">
			<view class="l"><image :src="item.course.thumbimage" class="br-10" /></view>
			<view class="r m-l-20">
				<view class="m-t-10 tb">
					{{item.course.name}}
				</view>
				<view class="m-t-15" :style="css.tcp">
					<text class="ts-26 tdl" :style="css.tcl">单价：{{item.course.price}}</text>
				</view>
				<view class="flex m-t-15 lh-54">
					<view :style="css.tcl">总次数: <text class="num" :style="css.tcm"> {{item.num}} </text>次</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: "xyPackageCourse",
		props: {
			item: {
				type: Object,
			},
		},
		data() {
			return {
				css:this.$xyfun.css(),
			};
		},
		methods: {
			
		}
	}
</script>
<style lang="scss">
	.xy-package-course{
		.item{
			.l,.l image{width: 240rpx;height: 180rpx;}
			.r{
				width: 370rpx;
				.num{margin:0 3rpx;}
			}
		}
	}
</style>
