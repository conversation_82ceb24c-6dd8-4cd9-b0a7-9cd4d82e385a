<template>
	<view v-if="tabBarList && currentRoute != ''">
		<view class="tab-bar" :style="{ backgroundColor: tabBarList.backgroundColor }">
			<view class="tabbar-border"></view>
			<view class="item" v-for="(item, index) in tabBarList.list" :key="index" @click="$xyfun.to(item.link)" v-if="item.show == 1">
				<view class="bd">
					<view class="icon">
						<block v-if="verify(item.link)">
							<image :src="$xyfun.image(item.selectedIconPath)" />
						</block>
						<block v-else>
							<image :src="$xyfun.image(item.iconPath)" />
						</block>
					</view>
					<view class="label p-t-5" :style="{ color: verify(item.link) ? tabBarList.textHoverColor : tabBarList.textColor }">
						{{ item.title }}
					</view>
				</view>
			</view>
		</view>
		
		<!-- 解决fixed定位后底部导航栏塌陷问题 -->
		<view class="tab-bar-placeholder"></view>
	</view>
</template>

<script>
export default {
	name: 'xyTabbar',
	props: {
	},
	data() {
		return {
			currentRoute: '', //当前页面路径
			tabBarList:{},
		};
	},
	mounted() {
		let currentPage = getCurrentPages()[getCurrentPages().length - 1];
		this.currentRoute = '/' + currentPage.route;
		this.tabBarList = this.$xyfun.tabBarData();
	},
	methods: {
		verify(link) {
			if (link == null || link == '') return false;
			var url = this.currentRoute;
			if (link == url) {
				return true;
			}
			return false;
		}
	}
};
</script>

<style lang="scss">

.safe-area {
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar {
	background-color: #fff;
	box-sizing: border-box;
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	z-index: 998;
	display: flex;
	border-top: 2rpx solid #f5f5f5;
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);

	.tabbar-border {
		background-color: rgba(255, 255, 255, 0.329412);
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 2rpx;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
	}

	.item {
		display: flex;
		align-items: center;
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		flex: 1;
		flex-direction: column;
		padding-bottom: 10rpx;
		box-sizing: border-box;

		.bd {
			position: relative;
			height: 100rpx;
			flex-direction: column;
			text-align: center;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.icon {
				position: relative;
				display: inline-block;
				margin-top: 10rpx;
				width: 44rpx;
				height: 44rpx;
				font-size: 44rpx;
				image {
					width: 100%;
					height: 100%;
				}
				> view {
					height: inherit;
					display: flex;
					align-items: center;
				}
				.bar-icon {
					font-size: 42rpx;
				}
			}

			.label {
				position: relative;
				text-align: center;
				font-size: 24rpx;
				line-height: 1;
				margin-top: 18rpx;
			}
		}

	}
}
.tab-bar-placeholder {
	padding-bottom: calc(constant(safe-area-inset-bottom) + 112rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 112rpx);
}
</style>
