<?php

namespace app\admin\controller\xycourse;

use app\common\controller\Backend;

/**
 * 门店管理
 *
 * @icon fa fa-circle-o
 */
class Course extends Backend
{

    /**
     * Course模型对象
     * @var \app\admin\model\xycourse\Course
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xycourse\Course;
        $this->view->assign("typeList", $this->model->getTypeList());
    }


    /**
     * 选择
     */
    public function select()
    {
        if ($this->request->isAjax()) {
            return $this->index();
        }
        $mimetype = $this->request->get('mimetype', '');
        $mimetype = substr($mimetype, -1) === '/' ? $mimetype . '*' : $mimetype;
        $this->view->assign('mimetype', $mimetype);
        return $this->view->fetch();
    }

    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }

}
