<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" min="0" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$row.store_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coach_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coach_id" data-rule="required" min="0" data-source="coach/index" class="form-control selectpage" name="row[coach_id]" type="text" value="{$row.coach_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Course_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_id" data-rule="required" min="0" data-source="course/index" class="form-control selectpage" name="row[course_id]" type="text" value="{$row.course_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-date" data-rule="required" class="form-control" name="row[date]" type="text" value="{$row.date|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Scheduling_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-scheduling_id" data-rule="required" data-source="scheduling/index" class="form-control selectpage" name="row[scheduling_id]" type="text" value="{$row.scheduling_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Timestr')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-timestr" data-rule="required" class="form-control" name="row[timestr]" type="text" value="{$row.timestr|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-type" data-rule="required" class="form-control selectpicker" name="row[type]">
                {foreach name="typeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="$row.type"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key|htmlentities}"><input id="row[status]-{$key|htmlentities}" name="row[status]" type="radio" value="{$key|htmlentities}" {in name="key" value="$row.status"}checked{/in} /> {$vo|htmlentities}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coachrealname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coachrealname" data-rule="required" class="form-control" name="row[coachrealname]" type="text" value="{$row.coachrealname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coachheadimage')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-coachheadimage" data-rule="required" class="form-control" size="50" name="row[coachheadimage]" type="text" value="{$row.coachheadimage|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-coachheadimage" class="btn btn-danger faupload" data-input-id="c-coachheadimage" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-coachheadimage"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-coachheadimage" class="btn btn-primary fachoose" data-input-id="c-coachheadimage" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-coachheadimage"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-coachheadimage"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coursename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coursename" data-rule="required" class="form-control" name="row[coursename]" type="text" value="{$row.coursename|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coursethumbimage')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-coursethumbimage" data-rule="required" class="form-control" size="50" name="row[coursethumbimage]" type="text" value="{$row.coursethumbimage|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-coursethumbimage" class="btn btn-danger faupload" data-input-id="c-coursethumbimage" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-coursethumbimage"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-coursethumbimage" class="btn btn-primary fachoose" data-input-id="c-coursethumbimage" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-coursethumbimage"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-coursethumbimage"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Storename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-storename" data-rule="required" class="form-control" name="row[storename]" type="text" value="{$row.storename|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
