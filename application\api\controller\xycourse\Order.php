<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;

use app\api\model\xycourse\order\Order as OrderModel;


/**
 * XYcourse 课程包订单接口
 */
class Order extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
    

    /**
	 * 列表
	 */
	public function lists()
    {
    	$params = $this->request->post();
        $data = OrderModel::getLists($params);
        $this->success('订单列表', $data);
    }

    public function init()
    {
        $params = $this->request->post();
        $order = OrderModel::orderInit($params);
        $this->success('添加成功', $order);
    }

    
	public function add()
    {
        $params = $this->request->post();
        $order = OrderModel::addOrder($params);
        $this->success('添加成功', $order);
    }

    public function detail()
    {
        $params = $this->request->post();
        $this->success('订单详情', OrderModel::getDetail($params));
    }

    public function cancel()
    {
        $params = $this->request->post();
        $this->success('取消成功', OrderModel::cancelOrder($params));
    }
	
	
}