<template>
	<view class="xy-package" :style="{'backgroundColor':data.params.bgColor,'border-radius': data.params.borderRadius+'rpx','margin':'0 '+ data.params.lrmargin+'rpx',padding:data.params.njj+'rpx','paddingBottom':0}"> 
		<view class="title flex p-t-5" v-if="data.params.title">
			<view class="line" :style="css.mcbg"></view>
			<view class="l tb lh-32 m-l-10">{{data.params.title}}</view>
			<view class="r ts-26 flex m-l-auto" :style="css.tcl" v-if="data.params.linktitle" @tap="$xyfun.to('/pages/package')">
				{{data.params.linktitle}}
				<text class="xyicon icon-right ts-34"></text>
			</view>
		</view>
		<div class="list" :style="{'marginTop':data.params.njj+'rpx'}">
			<view class="item" :style="{'paddingBottom':data.params.njj+'rpx'}" v-for="(item, keys) in data.data" :key="keys">
				<xy-package-row :item="item" :sales="false" />
			</view>
		</div>
	</view>
</template>
<script>
	
	import xyPackageRow from './row';
	export default {
		name: "xyPackage",
		components: {
			xyPackageRow,
		},
		props: {
			data: {
				type: Object,
				default: function() {
					return {
						name: '课程包组件',
						type: 'menu',
						params: [],
						data: []
					}
				}
			}
		},
		data() {
			return {
				css:this.$xyfun.css(),
			}
		},
		methods:{
			
		}
	}
</script>
<style lang="scss">
	.xy-package{
		.item{
			button{
				background: none; padding: 0;margin: 0;
			}
			button image{vertical-align: top;}
			button.lr{display: flex;width: 100%;text-align: center;margin: 0;justify-content: center;}
			button::after{border: none;}
		}
		.title{height: 32rpx;line-height: 32rpx;}
		.title .line{
			width: 6rpx;
			border-radius: 2rpx;
			height: 32rpx;
		}
	}
	
</style>