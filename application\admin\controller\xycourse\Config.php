<?php

namespace app\admin\controller\xycourse;

use app\common\controller\Backend;
use app\admin\model\xycourse\Config as ConfigModel;
use think\Exception;

/**
 * XYcourse配置
 */
class Config extends Backend
{

    /**
     * @var \app\admin\model\xycourse\Config
     */
    protected $model = null;
    protected $noNeedRight = [];


    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('app\admin\model\xycourse\Config');
    }

    /**
     * 查看
     */
    public function index()
    {
        return $this->view->fetch();
    }

    public function set($type)
    {
        if ($this->request->isPost()) {
            $data = $this->request->post("data");
            if ($data) {
                try {
                    $config = $this->model->get(['name' => $type]);
                    if(!$config) {
                        $this->model->allowField(true)->save([
                            'name' => $type,
                            'title' => $this->request->post("title"),
                            'group' => $this->request->post("group"),
                            'type' => 'array',
                            'value' => $data,
                        ]);
                    }else {
                        $config->value = $data;
                        $config->save();
                    }
                    
                } catch (Exception $e) {
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $config = $this->model->where(['name' => $type])->value('value');
        $config = json_decode($config, true);
        $this->assignconfig('type',$type);
        $this->assignconfig('row', $config);
        return $this->view->fetch();  
    }

    //添加
    public function add() {
        return;
    }
    //编辑
    public function edit($ids = null) {
        return;
    }
    //删除
    public function del($ids = null) {
        return;
    }
    //回收站列表
    public function recyclebin() {
        return;
    }
    //回收站(真实删除或清空)
    public function destroy($ids = null) {
        return;
    }
    //回收站还原
    public function restore($ids = null) {
        return;
    }
    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }
        


}
