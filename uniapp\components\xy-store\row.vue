<template>
	<view class="xy-store-row">
		<view class="item flex" :style="css.tcm" @tap="onLink(item)">
			<view class="l">
				<image :src="item.logo" class="br-10" />
			</view>
			<view class="r m-l-auto">
				<view class="name ts-32 tb flex">
					<view>{{item.name}}</view>
				</view>
				<view class="address ovh ts-28 m-t-15" :style="css.tcl">
					<text class="xyicon icon-loc m-r-5"></text> {{item.address}}
				</view>
				<view class="distance ts-28 m-t-25 flex" :style="css.tcl">
					{{$xyfun.distance(item.distance)}}
					<view class="more m-l-auto ts-24 flex tc-w" :style="text=='当前门店' ? css.prbg : css.mcbg">{{text}}</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: "xyStoreRow",
		props: {
			item: {
				type: Object,
			},
			text: {
				type: String,
				default:'选择门店'
			},
			first: {
				type: Boolean,
				default:false
			}
		},
		data() {
			return {
				css:this.$xyfun.css(),
			};
		},
		methods: {
			onLink(store){
				if(this.text == '选择门店'){
					uni.setStorageSync("xycourse:store_id",store.id);
					this.$xyfun.prePage().loadData();
					
					if(this.first){
						uni.reLaunch({
							url:'/pages/index'
						})
					}else{
						this.$xyfun.back();
					}
				}else if(this.text == '当前门店'){
					this.$xyfun.to('/pages/store/select')
				} 
			},
			
		}
	}
</script>
<style lang="scss">
	.xy-store-row{
		.item{
			.l{
				image{width: 152rpx;height: 152rpx;}
			}
			.r{
				width: 460rpx;
				.name{
					image{
						height: 34rpx;
					}
				}
				.more{
					height: 38rpx;line-height: 38rpx;border-radius: 19rpx;font-weight: normal;padding: 0 20rpx;
				}
				.address{height: 30rpx;line-height: 30rpx;}
			}
		}
	}
</style>
