<template>
	<view class="xy-banner ovh" :style="{'border-radius': data.params.borderRadius+'rpx','margin':'0 '+ data.params.lrmargin+'rpx','height':data.params.height+'rpx'}">
		<swiper :style="{height: data.params.height+'rpx'}" :indicator-dots="data.params.indicatorDots==1" :circular="true" :autoplay="data.params.autoplay==1" :interval="data.params.interval" :indicator-color="data.params.indicatorColor" :indicator-active-color="data.params.indicatorActiveColor" duration="500">
			<swiper-item v-for="(item, keys) in data.data" :key="keys">
				<view v-if="item.link =='contact'">
					<button open-type="contact"><image :src="$xyfun.image(item.image)" :style="{'height':data.params.height+'rpx','border-radius': data.params.borderRadius+'rpx'}" /></button>
				</view>
				<view v-else-if="item.link == 'share'">
					<button open-type="share"> <image :src="$xyfun.image(item.image)" :style="{'height':data.params.height+'rpx','border-radius': data.params.borderRadius+'rpx'}" /></button>
				</view>
				<view @tap="$xyfun.to(item.link)" v-else>
					<image :src="$xyfun.image(item.image)" :style="{'height':data.params.height+'rpx','border-radius': data.params.borderRadius+'rpx'}" />
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>
<script>
	export default {
		name: "xyBanner",
		props: {
			data: {
				type: Object,
				default: function() {
					return {
						name: '',
						type: '',
						params: [],
						style: [],
						data: []
					}
				}
			}
		}
	}
</script>
<style lang="scss">
	.xy-banner{
		position: relative;
		button{background: none;padding: 0;border-radius: 0;}
		swiper image{
			width: 100%;height: 100%;
		}
	}
</style>
