CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_appointment` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员',
  `coach_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '老师ID',
  `course_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '课程ID',
  `user_course_id` int(10) NOT NULL DEFAULT '0' COMMENT '会员课程ID',
  `date` date NOT NULL COMMENT '预约日期',
  `scheduling_id` int(10) NOT NULL DEFAULT '0' COMMENT '排班',
  `timestr` varchar(50) NOT NULL DEFAULT '' COMMENT '预约时段',
  `starttime` bigint(16) DEFAULT NULL COMMENT '开始时间',
  `endtime` bigint(16) DEFAULT NULL COMMENT '结束时间',
  `type` enum('league','private') CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'league' COMMENT '类型:league=团课,private=私教',
  `coachrealname` varchar(255) NOT NULL COMMENT '老师',
  `coachheadimage` varchar(255) NOT NULL COMMENT '老师头像',
  `coursename` varchar(255) NOT NULL COMMENT '课程',
  `coursethumbimage` varchar(255) NOT NULL COMMENT '课程缩略图',
  `ext` text COMMENT '附加字段',
  `cancel_user_id` int(10) NOT NULL DEFAULT '0' COMMENT '取消操作用户',
  `verify_user_id` int(10) NOT NULL DEFAULT '0' COMMENT '完成操作用户',
  `status` varchar(30) NOT NULL DEFAULT '1' COMMENT '状态: 1=待上课,2=已完成,-1=已取消',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预约表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_article` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `image` varchar(100) NOT NULL DEFAULT '' COMMENT '图片',
  `content` text NOT NULL COMMENT '内容',
  `views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文章表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_coach` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) DEFAULT '0' COMMENT '绑定会员',
  `headimage` varchar(200) NOT NULL COMMENT '头像',
  `realname` varchar(50) NOT NULL DEFAULT '' COMMENT '姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `jobnum` varchar(100) CHARACTER SET utf8 DEFAULT '' COMMENT '工号',
  `content` text NOT NULL COMMENT '介绍',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` varchar(30) NOT NULL DEFAULT 'normal' COMMENT '状态:normal=在职,hidden=离职',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='老师表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '变量名',
  `group` varchar(30) NOT NULL DEFAULT '' COMMENT '分组',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) NOT NULL DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) NOT NULL DEFAULT '' COMMENT '类型:string,text,int,bool,array,datetime,date,file',
  `value` longtext NOT NULL COMMENT '变量值',
  `content` longtext CHARACTER SET utf8mb4 COMMENT '变量字典数据',
  `rule` varchar(100) NOT NULL DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) NOT NULL DEFAULT '' COMMENT '扩展属性',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统配置表';


INSERT INTO `__PREFIX__xycourse_config` VALUES (1, 'xycourse', 'basic', '通用配置', '', 'array', '{\"name\":\"XYcourse课程预约\",\"logo\":\"/assets/addons/xycourse/imgs/logo.png\",\"useravatar\":\"/assets/addons/xycourse/imgs/logo.png\",\"store_id\":3,\"agreement\":1,\"privacy\":2,\"courseagree\":3}', NULL, '', '');
INSERT INTO `__PREFIX__xycourse_config` VALUES (2, 'appstyle', 'basic', '前端配置', '', 'array', '{\"mainColor\":\"#fc6d21\",\"navBarBgColor\":\"#fc6d21\",\"navBarFrontColor\":\"#ffffff\",\"pageBgColor\":\"#FFFCF7\",\"textMainColor\":\"#333333\",\"textLightColor\":\"#808080\",\"textPriceColor\":\"#fc6d21\",\"pageModuleBgColor\":\"#ffffff\"}', NULL, '', '');
INSERT INTO `__PREFIX__xycourse_config` VALUES (3, 'tabbar', 'tabbar', '底部导航', '', 'array', '{\"backgroundColor\":\"#FFFFFF\",\"textColor\":\"#282828\",\"textHoverColor\":\"#fc6d21\",\"list\":[{\"title\":\"首页\",\"link\":\"/pages/index\",\"iconPath\":\"/assets/addons/xycourse/imgs/tabbar/index.png\",\"selectedIconPath\":\"/assets/addons/xycourse/imgs/tabbar/index-a.png\",\"show\":1},{\"title\":\"约课\",\"link\":\"/pages/appointment\",\"iconPath\":\"/assets/addons/xycourse/imgs/tabbar/appointment.png\",\"selectedIconPath\":\"/assets/addons/xycourse/imgs/tabbar/appointment-a.png\",\"show\":1},{\"title\":\"购课\",\"link\":\"/pages/package\",\"iconPath\":\"/assets/addons/xycourse/imgs/tabbar/course.png\",\"selectedIconPath\":\"/assets/addons/xycourse/imgs/tabbar/course-a.png\",\"show\":1},{\"title\":\"我的\",\"link\":\"/pages/user\",\"iconPath\":\"/assets/addons/xycourse/imgs/tabbar/user.png\",\"selectedIconPath\":\"/assets/addons/xycourse/imgs/tabbar/user-a.png\",\"show\":1}]}', NULL, '', '');
INSERT INTO `__PREFIX__xycourse_config` VALUES (4, 'share', 'basic', '分享配置', '', 'array', '{\"title\":\"XYcourse课程预约\",\"image\":\"/assets/addons/xycourse/imgs/share.jpg\",\"user_poster_bg\":\"/assets/addons/xycourse/imgs/sharebg.jpg\"}', NULL, '', '');
INSERT INTO `__PREFIX__xycourse_config` VALUES (5, 'distribution', 'basic', '分销配置', '', 'array', '{\"open\":1,\"isdis\":1,\"child_condition\":\"share\",\"level\":\"2\"}', NULL, '', '');
INSERT INTO `__PREFIX__xycourse_config` VALUES (6, 'withdraw', 'basic', '提现配置', '', 'array', '{\"methods\":[],\"rate\":\"0.6\",\"min\":\"100\",\"max\":\"10000\"}', NULL, '', '');


CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_coupon` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` enum('reward','discount') NOT NULL DEFAULT 'reward' COMMENT '优惠券类型:reward=满减,discount=折扣',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `count` int(11) NOT NULL DEFAULT '0' COMMENT '发放数量',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠券面额',
  `leadcount` int(11) NOT NULL DEFAULT '0' COMMENT '已领取数量',
  `usedcount` int(11) NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `atleast` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '满多少元使用0无限制',
  `isshow` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否允许直接领取:0=否,1=是',
  `discount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '折扣',
  `validitytype` tinyint(4) NOT NULL DEFAULT '0' COMMENT '过期类型:0=固定时间范围过期,1=领取之日起,2=长期有效',
  `endusetime` bigint(16) DEFAULT NULL COMMENT '使用结束时间',
  `fixedterm` int(11) NOT NULL DEFAULT '0' COMMENT '有效天数',
  `maxfetch` int(11) NOT NULL DEFAULT '0' COMMENT '每人最大领取个数',
  `weight` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_course` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
  `type` enum('league','private') NOT NULL DEFAULT 'league' COMMENT '类型:league=团课,private=私教',
  `thumbimage` varchar(255) NOT NULL COMMENT '缩略图',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='场馆表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_distribution` (
  `user_id` int(10) unsigned NOT NULL COMMENT '用户',
  `level_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '等级',
  `pid` int(10) unsigned NOT NULL COMMENT '上级分销商',
  `realname` varchar(100) NOT NULL COMMENT '姓名',
  `mobile` varchar(20) NOT NULL COMMENT '手机号',
  `commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可提现佣金',
  `total_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总佣金',
  `withdrawn_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '已提现佣金',
  `withdrawing_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现中佣金',
  `status` varchar(100) NOT NULL DEFAULT 'normal' COMMENT '分销商状态:normal=正常,forbidden=禁用',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分销商';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_distribution_commission` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户',
  `distribution_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分销商',
  `type` varchar(100) NOT NULL DEFAULT 'withdraw' COMMENT '类型: apply_withdraw=申请提现,refuse_withdraw=拒绝提现,pay_withdraw=提现打款,order=订单结算,sys=后台操作',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '费用',
  `before` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更前',
  `after` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更后',
  `service_id` int(10) NOT NULL DEFAULT '0' COMMENT '业务ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分销商佣金流水表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_distribution_level` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `grade` enum('1','2','3','4','5','6','7','8','9','10') NOT NULL DEFAULT '1' COMMENT '等级:1=一级,2=二级,3=三级,4=四级,5=五级,6=六级,7=七级,8=八级,9=九级,10=十级',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '等级名称',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '等级徽章',
  `commission_one` int(3) unsigned NOT NULL DEFAULT '0' COMMENT '一级佣金比例',
  `commission_two` int(3) unsigned NOT NULL DEFAULT '0' COMMENT '二级佣金比例',
  `commission_team` int(3) NOT NULL DEFAULT '0' COMMENT '团队业绩佣金比例',
  `commission_bonus` int(3) NOT NULL DEFAULT '0' COMMENT '日交易额分红比例',
  `upgrade_type` enum('or','and') NOT NULL DEFAULT 'or' COMMENT '升级方式',
  `upgrade_rules` text COMMENT '升级条件',
  `status` varchar(100) NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分销商等级';

INSERT INTO `__PREFIX__xycourse_distribution_level` VALUES (1, '1', '一级', '/assets/addons/xycourse/imgs/level.png', 10, 5, 0, 0, 'or', NULL, 'normal', 1704616412, 1704616412);

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_distribution_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `service_order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务订单',
  `buyer_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '买家',
  `order_sn` varchar(60) NOT NULL COMMENT '订单号',
  `order_type` varchar(100) NOT NULL COMMENT '订单类型:order=课程包',
  `commission_event` varchar(255) DEFAULT 'payed' COMMENT '佣金结算方式: payed=支付后',
  `one_distribution_id` int(10) DEFAULT NULL COMMENT '一级分销商',
  `two_distribution_id` int(10) DEFAULT NULL COMMENT '二级分销商',
  `one_commission` decimal(10,2) DEFAULT NULL COMMENT '一级佣金',
  `two_commission` decimal(10,2) DEFAULT NULL COMMENT '二级佣金',
  `corich_commission` decimal(10,2) DEFAULT '0.00' COMMENT '共富收益',
  `total_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `status` tinyint(1) DEFAULT '0' COMMENT '佣金处理状态:-2=已取消,-1=已退回,0=未结算,1=已结算',
  `settle_time` bigint(16) DEFAULT NULL COMMENT '结算时间',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分销订单表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_link` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` varchar(100) NOT NULL DEFAULT 'basic' COMMENT '链接类型',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '链接名称',
  `url` varchar(100) NOT NULL DEFAULT '' COMMENT '路径',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='链接表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_sn` varchar(60) NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '下单会员',
  `store_id` int(10) NOT NULL DEFAULT '0' COMMENT '门店ID',
  `package_id` int(10) NOT NULL COMMENT '课程包ID',
  `packagename` varchar(200) NOT NULL DEFAULT '' COMMENT '课程包名称',
  `packagethumbimage` varchar(255) NOT NULL DEFAULT '' COMMENT '课程包缩略图',
  `limitday` int(10) NOT NULL DEFAULT '0' COMMENT '有效期(天)',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `coupon_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `total_fee` decimal(10,2) DEFAULT '0.00' COMMENT '需付金额',
  `pay_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额',
  `transaction_id` varchar(60) DEFAULT NULL COMMENT '交易单号',
  `payment_json` varchar(2500) DEFAULT NULL COMMENT '交易原始数据',
  `pay_type` varchar(100) NOT NULL DEFAULT '' COMMENT '支付方式:wechat=微信支付,balance=余额支付',
  `paytime` bigint(16) DEFAULT NULL COMMENT '支付时间',
  `platform` varchar(100) NOT NULL DEFAULT 'wxMiniProgram' COMMENT '平台:wxMiniProgram=微信小程序,wxOfficialAccount=微信公众号',
  `ext` text COMMENT '附加字段',
  `user_coupon_id` int(10) NOT NULL DEFAULT '0' COMMENT '用户优惠券ID',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '订单状态:-2=已关闭,-1=已取消,0=待付款,1=已付款',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `order_sn` (`order_sn`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='定场订单表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_order_item` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` int(10) NOT NULL DEFAULT '0' COMMENT '订单',
  `course_id` int(10) NOT NULL DEFAULT '0' COMMENT '课程ID',
  `coursename` varchar(200) NOT NULL DEFAULT '' COMMENT '课程名称',
  `coursethumbimage` varchar(255) NOT NULL DEFAULT '' COMMENT '课程缩略图',
  `courseprice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '课程单价',
  `coursetype` varchar(100) NOT NULL DEFAULT '' COMMENT '课程类型',
  `num` int(10) NOT NULL COMMENT '次数',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='课程包选项';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_package` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '名称',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '售价',
  `virtualsales` int(10) NOT NULL COMMENT '虚拟销量',
  `sales` int(10) NOT NULL DEFAULT '0' COMMENT '销量',
  `limitday` int(10) NOT NULL DEFAULT '0' COMMENT '有效期(天)',
  `thumbimage` varchar(255) NOT NULL COMMENT '缩略图',
  `images` text NOT NULL COMMENT '轮播图',
  `content` text NOT NULL COMMENT '使用说明',
  `is_dis` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否分销: 1=参与,0=不参与',
  `dis_rule` tinyint(2) NOT NULL DEFAULT '0' COMMENT '分销规则: 0=默认规则 ,1=单独设置',
  `commission_rule` text COMMENT '单独设置佣金规则',
  `status` varchar(100) NOT NULL DEFAULT 'up' COMMENT '状态:normal=上架,hidden=下架',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='课程包';

ALTER TABLE `__PREFIX__xycourse_package` ADD COLUMN `is_dis` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否分销: 1=参与,0=不参与' AFTER `content`;
ALTER TABLE `__PREFIX__xycourse_package` ADD COLUMN `dis_rule` tinyint(2) NOT NULL DEFAULT '0' COMMENT '分销规则: 0=默认规则 ,1=单独设置' AFTER `is_dis`;
ALTER TABLE `__PREFIX__xycourse_package` ADD COLUMN `commission_rule` text NULL COMMENT '单独设置佣金规则' AFTER `dis_rule`;

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_package_item` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `package_id` int(10) NOT NULL COMMENT '课程包',
  `course_id` int(10) NOT NULL DEFAULT '0' COMMENT '课程',
  `num` int(10) NOT NULL DEFAULT '1' COMMENT '次数',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='课程包选项';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_page` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `page_token` varchar(16) NOT NULL DEFAULT '' COMMENT '页面Token',
  `name` varchar(100) NOT NULL DEFAULT '自定义页面' COMMENT '页面名称',
  `cover` varchar(256) DEFAULT NULL COMMENT '页面封面',
  `type` varchar(100) NOT NULL DEFAULT 'index' COMMENT '模板类型:index=首页模板',
  `page` longtext COMMENT '页面配置',
  `item` longtext COMMENT '项目',
  `is_use` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否使用',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='自定义装修页面表';

INSERT INTO `__PREFIX__xycourse_page` (`id`, `page_token`, `name`, `cover`, `type`, `page`, `item`, `is_use`, `status`, `createtime`, `updatetime`, `deletetime`) VALUES (1, 'YQXpLKb6axq0Docm', '首页', '/assets/addons/xycourse/imgs/index.jpg', 'index', '{\"params\":{\"navigationBarTitleText\":\"XYcourse\\u8bfe\\u7a0b\\u9884\\u7ea6\"},\"style\":{\"navigationBarTextStyle\":\"#ffffff\",\"navigationBarBackgroundColor\":\"#fc581c\",\"pageBackgroundColor\":\"#FFFCF7\"}}', '[{\"name\":\"\\u7a7a\\u767d\\u884c\",\"type\":\"empty\",\"icon\":\"window-maximize\",\"params\":{\"height\":\"30\"}},{\"name\":\"\\u8f6e\\u64ad\\u7ec4\\u4ef6\",\"type\":\"banner\",\"icon\":\"random\",\"params\":{\"autoplay\":\"1\",\"interval\":\"5000\",\"height\":\"370\",\"indicatorDots\":\"1\",\"indicatorColor\":\"#ffffff\",\"indicatorActiveColor\":\"#fc6d21\",\"lrmargin\":\"30\",\"borderRadius\":\"20\"},\"data\":[{\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/banner.png\",\"link\":\"\\/pages\\/store\\/detail\"},{\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/banner.png\",\"link\":\"\\/pages\\/store\\/detail\"},{\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/banner.png\",\"link\":\"\\/pages\\/store\\/detail\"},{\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/banner.png\",\"link\":\"\\/pages\\/store\\/detail\"}]},{\"name\":\"\\u7a7a\\u767d\\u884c\",\"type\":\"empty\",\"icon\":\"window-maximize\",\"params\":{\"height\":\"30\"}},{\"name\":\"\\u7a7a\\u767d\\u884c\",\"type\":\"empty\",\"icon\":\"window-maximize\",\"params\":{\"height\":\"10\"}},{\"name\":\"\\u83dc\\u5355\\u7ec4\\u4ef6\",\"type\":\"menu\",\"icon\":\"list\",\"params\":{\"title\":\"\",\"linktitle\":\"\",\"link\":\"\",\"colnum\":\"2\",\"textimgpl\":\"1\",\"lrmargin\":\"30\",\"borderRadius\":\"0\",\"itemborderRadius\":\"10\",\"bgColor\":\"#FFFCF7\",\"itembgColor\":\"#ffffff\",\"imgwh\":\"80\",\"textsize\":\"30\",\"itemjj\":\"30\",\"itemnjj\":\"30\",\"textbold\":\"1\",\"njj\":\"0\",\"textColor\":\"#333333\"},\"data\":[{\"name\":\"\\u798f\\u5229\\u9886\\u53d6\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/home1.png\",\"link\":\"\\/pages\\/coupon\\/list\"},{\"name\":\"\\u6211\\u8981\\u5145\\u503c\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/home2.png\",\"link\":\"\\/pages\\/user\\/balance\\/recharge\"},{\"name\":\"\\u6211\\u7684\\u9884\\u7ea6\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/home3.png\",\"link\":\"pages\\/user\\/appointment\\/list\"},{\"name\":\"\\u95e8\\u5e97\\u4ecb\\u7ecd\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/home4.png\",\"link\":\"\\/pages\\/store\\/detail\"}]},{\"name\":\"\\u8bfe\\u7a0b\\u5305\\u7ec4\\u4ef6\",\"type\":\"package\",\"icon\":\"archive\",\"dataType\":\"package\",\"data\":[{\"id\":\"2\",\"name\":\"\\u6d4b\\u8bd5\\u56e2\\u8bfe\\u8bfe\\u7a0b\\u5305\",\"price\":\"899.00\",\"virtualsales\":\"100\",\"sales\":\"0\",\"limitday\":\"80\",\"thumbimage\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/course.png\",\"images\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/store.png,\\/assets\\/addons\\/xycourse\\/imgs\\/course.png,\\/assets\\/addons\\/xycourse\\/imgs\\/course.png\",\"content\":\"<p>\\u8d2d\\u4e70\\u8bfe\\u7a0b\\u5305\\uff0c\\u9884\\u7ea6\\u4e0a\\u8bfe\\u3002<br><\\/p>\",\"is_dis\":\"1\",\"dis_rule\":\"0\",\"commission_rule\":\"\",\"status\":\"normal\",\"createtime\":\"1704438475\",\"updatetime\":\"1704694074\",\"state\":\"true\"},{\"id\":\"1\",\"name\":\"\\u6d4b\\u8bd5\\u79c1\\u6559\\u8bfe\\u7a0b\\u5305\",\"price\":\"1299.00\",\"virtualsales\":\"100\",\"sales\":\"0\",\"limitday\":\"90\",\"thumbimage\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/course.png\",\"images\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/store.png,\\/assets\\/addons\\/xycourse\\/imgs\\/course.png,\\/assets\\/addons\\/xycourse\\/imgs\\/course.png\",\"content\":\"<p>\\u8d2d\\u4e70\\u8bfe\\u7a0b\\u5305\\uff0c\\u9884\\u7ea6\\u4e0a\\u8bfe\\u3002<\\/p>\",\"is_dis\":\"1\",\"dis_rule\":\"1\",\"commission_rule\":\"[{\\\"level_id\\\":1,\\\"level_name\\\":\\\"\\u4e00\\u7ea7\\\",\\\"commission_one\\\":\\\"15\\\",\\\"commission_two\\\":\\\"8\\\"}]\",\"status\":\"normal\",\"createtime\":\"1704433269\",\"updatetime\":\"1704694107\",\"state\":\"true\"}],\"params\":{\"title\":\"\\u8bfe\\u7a0b\\u5305\",\"linktitle\":\"\\u66f4\\u591a\",\"lrmargin\":\"30\",\"bgColor\":\"#ffffff\",\"borderRadius\":\"20\",\"njj\":\"30\"}},{\"name\":\"\\u7a7a\\u767d\\u884c\",\"type\":\"empty\",\"icon\":\"window-maximize\",\"params\":{\"height\":\"30\"}},{\"name\":\"\\u95e8\\u5e97\\u7ec4\\u4ef6\",\"type\":\"store\",\"icon\":\"columns\",\"params\":{\"bgColor\":\"#ffffff\",\"borderRadius\":\"20\",\"lrmargin\":\"30\",\"njj\":\"30\",\"logo\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/logo.png\",\"name\":\"XYcourse\\u8bfe\\u7a0b\\u9884\\u7ea6\\u6d4b\\u8bd5\\u95e8\\u5e97\",\"address\":\"\\u6e56\\u5357\\u7701\\/\\u682a\\u6d32\\u5e02\\/\\u5929\\u5143\\u533a\\u8be6\\u7ec6\\u5730\\u5740\",\"businesshours\":\"08:30-21:30\",\"phone\":\"***********\",\"weixin\":\"***********\",\"longitude\":\"113.810533\",\"latitude\":\"23.303926\"}}]', 1, 'normal', 1704694134, 1704694134, NULL);
INSERT INTO `__PREFIX__xycourse_page` (`id`, `page_token`, `name`, `cover`, `type`, `page`, `item`, `is_use`, `status`, `createtime`, `updatetime`, `deletetime`) VALUES (2, 's2vVJQ40olb8NW35', '我的', '/assets/addons/xycourse/imgs/user.jpg', 'user', '{\"params\":{\"navigationBarTitleText\":\"\\u6211\\u7684\"},\"style\":{\"navigationBarTextStyle\":\"#ffffff\",\"navigationBarBackgroundColor\":\"#fc6d21\",\"pageBackgroundColor\":\"#FFFCF7\"}}', '[{\"name\":\"\\u7a7a\\u767d\\u884c\",\"type\":\"empty\",\"icon\":\"window-maximize\",\"params\":{\"height\":\"30\"}},{\"name\":\"\\u7528\\u6237\\u5361\\u7247\",\"type\":\"user-card\",\"icon\":\"user\",\"params\":{\"bgColor\":\"#ffffff\",\"borderRadius\":\"20\",\"lrmargin\":\"30\",\"njj\":\"20\"}},{\"name\":\"\\u7a7a\\u767d\\u884c\",\"type\":\"empty\",\"icon\":\"window-maximize\",\"params\":{\"height\":\"30\"}},{\"name\":\"\\u94b1\\u5305\\u6a21\\u5757\",\"type\":\"user-money\",\"icon\":\"jpy\",\"params\":{\"bgColor\":\"#ffffff\",\"borderRadius\":\"20\",\"lrmargin\":\"30\",\"njj\":\"20\",\"rechargeicon\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/recharge.png\",\"walleticon\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/wallet.png\"}},{\"name\":\"\\u7a7a\\u767d\\u884c\",\"type\":\"empty\",\"icon\":\"window-maximize\",\"params\":{\"height\":\"30\"}},{\"name\":\"\\u83dc\\u5355\\u7ec4\\u4ef6\",\"type\":\"menu\",\"icon\":\"list\",\"params\":{\"title\":\"\\u6211\\u7684\\u670d\\u52a1\",\"linktitle\":\"\",\"link\":\"\",\"colnum\":\"3\",\"textimgpl\":\"2\",\"lrmargin\":\"30\",\"borderRadius\":\"0\",\"itemborderRadius\":\"0\",\"bgColor\":\"#ffffff\",\"itembgColor\":\"#ffffff\",\"imgwh\":\"70\",\"textsize\":\"30\",\"itemjj\":\"0\",\"itemnjj\":\"30\",\"textbold\":\"0\",\"njj\":\"30\",\"textColor\":\"#333333\"},\"data\":[{\"name\":\"\\u6211\\u7684\\u9884\\u7ea6\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/us0.png\",\"link\":\"\\/pages\\/user\\/appointment\\/list\"},{\"name\":\"\\u6211\\u7684\\u8bfe\\u7a0b\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/us1.png\",\"link\":\"\\/pages\\/user\\/course\\/list\"},{\"name\":\"\\u6211\\u7684\\u8ba2\\u5355\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/us2.png\",\"link\":\"\\/pages\\/user\\/order\\/list\"},{\"name\":\"\\u4f18\\u60e0\\u5238\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/us7.png\",\"link\":\"\\/pages\\/user\\/coupon\\/list\"},{\"name\":\"\\u5206\\u9500\\u4e2d\\u5fc3\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/us6.png\",\"link\":\"\\/pages\\/distribution\\/center\"},{\"name\":\"\\u6211\\u7684\\u8d44\\u6599\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/us4.png\",\"link\":\"\\/pages\\/user\\/info\"}]},{\"name\":\"\\u7a7a\\u767d\\u884c\",\"type\":\"empty\",\"icon\":\"window-maximize\",\"params\":{\"height\":\"30\"}},{\"name\":\"\\u83dc\\u5355\\u7ec4\\u4ef6\",\"type\":\"menu\",\"icon\":\"list\",\"params\":{\"title\":\"\",\"linktitle\":\"\",\"link\":\"\",\"colnum\":\"2\",\"textimgpl\":\"1\",\"lrmargin\":\"30\",\"borderRadius\":\"0\",\"itemborderRadius\":\"10\",\"bgColor\":\"#FFFCF7\",\"itembgColor\":\"#ffffff\",\"imgwh\":\"80\",\"textsize\":\"30\",\"itemjj\":\"30\",\"itemnjj\":\"30\",\"textbold\":\"1\",\"njj\":\"0\",\"textColor\":\"#333333\"},\"data\":[{\"name\":\"\\u8001\\u5e08\\u4e2d\\u5fc3\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/us6.png\",\"link\":\"\\/pages\\/coach\\/center\"},{\"name\":\"\\u5458\\u5de5\\u4e2d\\u5fc3\",\"image\":\"\\/assets\\/addons\\/xycourse\\/imgs\\/us8.png\",\"link\":\"\\/pages\\/staff\\/center\"}]}]', 1, 'normal', 1704761599, 1704761599, NULL);

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_recharge` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `facevalue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '面值',
  `buyprice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '售价',
  `status` varchar(30) NOT NULL DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='充值套餐表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_recharge_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_sn` varchar(60) NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `total_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `pay_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际支付金额',
  `transaction_id` varchar(60) DEFAULT NULL COMMENT '交易单号',
  `payment_json` varchar(2500) DEFAULT NULL COMMENT '交易原始数据',
  `pay_type` enum('wechat') DEFAULT 'wechat',
  `paytime` bigint(16) DEFAULT NULL COMMENT '支付时间',
  `ext` text CHARACTER SET utf8mb4 COMMENT '附加字段',
  `platform` enum('wxMiniProgram','wxOfficialAccount') DEFAULT 'wxMiniProgram' COMMENT '平台:wxMiniProgram=微信小程序,wxOfficialAccount=微信公众号',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '订单状态:-2=交易关闭,-1=已取消,0=未支付,1=已支付',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `order_sn` (`order_sn`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `createtime` (`createtime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='充值订单表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_scheduling` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `coach_id` int(10) NOT NULL DEFAULT '0' COMMENT '老师',
  `course_id` int(10) NOT NULL DEFAULT '0' COMMENT '课程',
  `type` enum('league','private') NOT NULL DEFAULT 'league' COMMENT '类型:league=团课,private=私教',
  `date` date NOT NULL COMMENT '日期',
  `starttime` bigint(16) NOT NULL COMMENT '开始时间',
  `endtime` bigint(16) NOT NULL COMMENT '结束时间',
  `timestr` varchar(100) NOT NULL DEFAULT '' COMMENT '预约时段',
  `people` int(10) NOT NULL DEFAULT '0' COMMENT '可预约人数',
  `agreed` int(10) NOT NULL DEFAULT '0' COMMENT '已约人数',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='排班';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_share` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户',
  `share_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分享人',
  `page` varchar(100) NOT NULL DEFAULT '' COMMENT '分享页面',
  `page_id` int(10) NOT NULL DEFAULT '0' COMMENT '分享页面ID',
  `platform` varchar(20) DEFAULT NULL COMMENT '分享平台',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户分享';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_staff` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) DEFAULT '0' COMMENT '绑定会员',
  `headimage` varchar(200) NOT NULL DEFAULT '' COMMENT '头像',
  `realname` varchar(50) NOT NULL DEFAULT '' COMMENT '姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` varchar(30) NOT NULL DEFAULT 'normal' COMMENT '状态:normal=在职,hidden=离职',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门店员工表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_store`;
CREATE TABLE `__PREFIX__xycourse_store` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
  `businesshours` varchar(255) NOT NULL DEFAULT '' COMMENT '营业时间',
  `service_ids` varchar(255) NOT NULL DEFAULT '' COMMENT '基础服务',
  `logo` varchar(255) NOT NULL DEFAULT '' COMMENT 'Logo',
  `images` text NOT NULL COMMENT '场馆图片',
  `contacts` varchar(50) NOT NULL COMMENT '联系人',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `weixin` varchar(100) NOT NULL DEFAULT '' COMMENT '联系微信',
  `city` varchar(50) NOT NULL COMMENT '省市区',
  `address` varchar(255) NOT NULL COMMENT '详细地址',
  `videofile` varchar(255) NOT NULL COMMENT '门店视频',
  `latitude` varchar(100) NOT NULL DEFAULT '' COMMENT '纬度',
  `longitude` varchar(100) NOT NULL DEFAULT '' COMMENT '经度',
  `content` text NOT NULL COMMENT '场馆介绍',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门店表';

INSERT INTO `__PREFIX__xycourse_store` VALUES (1, 'XYcourse课程预约测试门店', '08:30-21:30', '4,2,1,3', '/assets/addons/xycourse/imgs/logo.png', '/assets/addons/xycourse/imgs/store.png,/assets/addons/xycourse/imgs/store.png,/assets/addons/xycourse/imgs/store.png,/assets/addons/xycourse/imgs/store.png', '朱工', '***********', '***********', '湖南省/株洲市/天元区', '详细地址', '', '23.303926', '113.810533', '<p>所有的美好都是有备而来，集美体塑形，心灵成长，美颜养生，有所为，有所爱，对生活有所期待。<br></p>', 1701917656, 1704699487);

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_store_service` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
  `logoimage` varchar(255) NOT NULL DEFAULT '' COMMENT '图标',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` varchar(30) NOT NULL DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='场馆基础服务表';

INSERT INTO `__PREFIX__xycourse_store_service` VALUES (1, '无线网络', '/assets/addons/xycourse/imgs/service1.png', 30, 'normal', 1682477516, 1682479867);
INSERT INTO `__PREFIX__xycourse_store_service` VALUES (2, '充电宝', '/assets/addons/xycourse/imgs/service2.png', 31, 'normal', 1682477655, 1682479599);
INSERT INTO `__PREFIX__xycourse_store_service` VALUES (3, '洗手间', '/assets/addons/xycourse/imgs/service3.png', 36, 'normal', 1683613423, 1701482696);
INSERT INTO `__PREFIX__xycourse_store_service` VALUES (4, '停车位', '/assets/addons/xycourse/imgs/service4.png', 37, 'normal', 1683613446, 1701482701);

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_third` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `platform` varchar(30) NOT NULL DEFAULT '' COMMENT '平台',
  `openid` varchar(100) NOT NULL DEFAULT '' COMMENT 'openid',
  `session_key` varchar(255) NOT NULL DEFAULT '' COMMENT 'session_key',
  `logintime` bigint(16) unsigned DEFAULT NULL COMMENT '登录时间',
  `createtime` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`,`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='第三方登录表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_user_account` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户',
  `realname` varchar(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `type` varchar(32) NOT NULL DEFAULT 'bank' COMMENT '账户类型 alipay=支付宝 ,bank=银行卡',
  `account_name` varchar(50) NOT NULL DEFAULT '' COMMENT '账号名称',
  `account_no` varchar(50) NOT NULL DEFAULT '' COMMENT '账号',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户提现账号';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_user_coupon` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL COMMENT '归属会员',
  `coupon_id` int(10) NOT NULL DEFAULT '0' COMMENT '优惠券',
  `type` enum('reward','discount') NOT NULL DEFAULT 'reward' COMMENT '优惠券类型:reward=满减,discount=折扣',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `useorderid` int(10) DEFAULT '0' COMMENT '使用订单ID',
  `useordertype` varchar(100) DEFAULT '0' COMMENT '使用订单ID',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '面额',
  `atleast` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '满多少元可使用',
  `discount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '折扣',
  `ishandsel` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否可转增:0=否,1=是',
  `handuserid` int(10) NOT NULL DEFAULT '0' COMMENT '赠送用户ID',
  `gettype` tinyint(4) NOT NULL DEFAULT '0' COMMENT '获取方式:1=直接领取,2=系统赠送,3=转赠',
  `usetime` bigint(16) DEFAULT NULL COMMENT '使用时间',
  `endtime` bigint(16) DEFAULT NULL COMMENT '到期时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态:0=未使用,1=已使用,3=已转赠',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_user_course` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户',
  `store_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上课门店',
  `course_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '课程ID',
  `user_package_id` int(10) NOT NULL DEFAULT '0' COMMENT '用户课程包',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '课程名称',
  `type` enum('league','private') NOT NULL DEFAULT 'league' COMMENT '类型:league=团课,private=私教',
  `thumbimage` varchar(255) NOT NULL DEFAULT '' COMMENT '课程图片',
  `num` int(10) NOT NULL DEFAULT '0' COMMENT '总次数',
  `residue` int(10) NOT NULL DEFAULT '0' COMMENT '剩余次数',
  `duedate` date NOT NULL COMMENT '到期日期',
  `createtime` bigint(16) DEFAULT NULL COMMENT '添加时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户课程';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_user_course_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `store_id` int(10) NOT NULL DEFAULT '0' COMMENT '门店',
  `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '用户',
  `user_package_id` int(10) NOT NULL COMMENT '用户课程包',
  `user_course_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户课程',
  `num` int(10) NOT NULL DEFAULT '0' COMMENT '变更节数',
  `type` varchar(100) NOT NULL DEFAULT 'buy' COMMENT '变更类型:buy=购买课程包,verify=消课,appointment=预约',
  `operate_user_id` int(10) NOT NULL DEFAULT '0' COMMENT '操作用户ID',
  `createtime` bigint(16) DEFAULT NULL COMMENT '添加时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户课程记录';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_user_money` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员',
  `type` varchar(100) NOT NULL DEFAULT '' COMMENT '类型',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '费用',
  `before` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更前',
  `after` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更后',
  `service_id` int(10) NOT NULL DEFAULT '0' COMMENT '业务ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员余额明细表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_user_package` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '下单会员',
  `store_id` int(10) NOT NULL DEFAULT '0' COMMENT '门店ID',
  `package_id` int(10) NOT NULL COMMENT '课程包ID',
  `packagename` varchar(200) NOT NULL DEFAULT '' COMMENT '课程包名称',
  `packagethumbimage` varchar(255) NOT NULL DEFAULT '' COMMENT '课程包缩略图',
  `duedate` date NOT NULL COMMENT '到期日期',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户课程包表';

CREATE TABLE IF NOT EXISTS `__PREFIX__xycourse_user_withdraw` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '用户id',
  `store_id` int(10) NOT NULL DEFAULT '0' COMMENT '门店',
  `withdraw_sn` varchar(60) NOT NULL DEFAULT '' COMMENT '提现交易号',
  `type` enum('balance','distribution','store') NOT NULL DEFAULT 'balance',
  `apply_money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现申请金额',
  `realname` varchar(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `mobile` varchar(11) NOT NULL DEFAULT '' COMMENT '手机号',
  `account_type` varchar(50) NOT NULL DEFAULT '' COMMENT '提现账号类型',
  `account_name` varchar(255) NOT NULL DEFAULT '' COMMENT '提现账号名称',
  `service_money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现手续费',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际转账金额',
  `audit_time` bigint(16) DEFAULT NULL COMMENT '审核时间',
  `payment_time` bigint(16) DEFAULT NULL COMMENT '转账时间',
  `account_no` varchar(255) NOT NULL DEFAULT '' COMMENT '收款账号',
  `rate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现手续费比率',
  `refuse_reason` varchar(500) NOT NULL DEFAULT '' COMMENT '拒绝原因',
  `memo` varchar(100) NOT NULL DEFAULT '' COMMENT '备注',
  `platform` enum('wxMiniProgram','wxOfficialAccount') DEFAULT 'wxMiniProgram' COMMENT '平台:wxMiniProgram=微信小程序,wxOfficialAccount=微信公众号',
  `status` int(3) NOT NULL DEFAULT '0' COMMENT '状态0待审核1.待转账2已转账 -1拒绝',
  `createtime` bigint(16) DEFAULT NULL COMMENT '申请时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员提现表';


ALTER TABLE `__PREFIX__user` ADD COLUMN `xycourse_parent_user_id` int(10) NULL DEFAULT 0 COMMENT '上级ID,0=无' AFTER `verification`;
ALTER TABLE `__PREFIX__user` ADD COLUMN `xycourse_consume` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '消费金额' AFTER `xycourse_parent_user_id`;
ALTER TABLE `__PREFIX__user` ADD COLUMN `xycourse_recharge` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '充值金额' AFTER `xycourse_consume`;

