<?php

namespace addons\xycourse\service;

use app\api\model\xycourse\user\User as UserModel;
use app\api\model\xycourse\coach\Coach as CoachModel;
use app\api\model\xycourse\store\Store as StoreModel;

/**
 * 老师业务
 */
class Coach
{

    public $user;     // 用户
    public $coach;    // 老师
    public $store;   // 门店

    // 老师状态
    const COACH_STATUS_NORMAL = 'normal';       // 正常 
    const COACH_STATUS_NULL = NULL;             // 未成为老师
    const COACH_STATUS_HIDDEN = 'hidden';       // 离职

    public function __construct($user_id)
    {
        $this->user = UserModel::where('id', $user_id)->field('id, nickname, username,mobile')->find();
        $this->store = StoreModel::get(1);
        $this->coach = CoachModel::where(['user_id'=>$user_id])->find();
    }

    // 获取老师状态
    public function getCoachStatus()
    {
        $coachStatus = 'normal';

        if (empty($this->coach)) {
            $coachStatus = self::COACH_STATUS_NULL;
        }else{
            $coachStatus = $this->coach->status;
        }

        $response = [
            'status' => $coachStatus,
            'msg'    => ''
        ];

        switch ($coachStatus) {
            case self::COACH_STATUS_NULL:
                $response['msg'] = '您不是门店的老师';
                break;
            case self::COACH_STATUS_HIDDEN:
                $response['msg'] = '您已离职';
                break;
        }
        return $response;
    }
    
    
}
