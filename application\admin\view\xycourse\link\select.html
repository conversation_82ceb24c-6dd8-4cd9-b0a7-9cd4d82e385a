<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="type">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="typeList" item="vo"}
            <li><a href="#t-{$key|htmlentities}" data-value="{$key|htmlentities}" data-toggle="tab">{$vo|htmlentities}</a></li>
            {/foreach}
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh')}
                        <a class="btn btn-info btn-load" href="javascript:;"><i class="fa fa-play"></i> 生成链接</a>
                        {if request()->get('multiple') == 'true'}
                        <a class="btn btn-danger btn-choose-multi"><i class="fa fa-check"></i> {:__('Choose')}</a>
                        {/if}
                    </div>
                    <table id="table" class="table table-bordered table-hover" width="100%">

                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
