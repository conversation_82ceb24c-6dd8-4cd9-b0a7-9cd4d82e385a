<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;

use app\api\model\xycourse\user\Coupon as UserCouponModel;

/**
 * XYcourse用户优惠券接口
 */
class UserCoupon extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
    

	/**
	 * 列表
	 */
	public function lists()
    {
    	$params = $this->request->post();
        $params['user_id'] = $this->auth->id;
        $data = UserCouponModel::getLists($params);
        $this->success('账户列表', $data);
    }

    /**
     * 转增
     */
    public function handsel(){
        $params = $this->request->post();
        $data = UserCouponModel::handsel($params);
        $this->success('转增成功', $data);
    }

    
}