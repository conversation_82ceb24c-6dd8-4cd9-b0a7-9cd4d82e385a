<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;

use app\api\model\xycourse\package\Package as PackageModel;


/**
 * XYcourse 课程包接口
 */
class Package extends Api
{
    protected $noNeedLogin = ['lists','detail'];
    protected $noNeedRight = ['*'];
    

    /**
	 * 列表
	 */
	public function lists()
    {
    	$params = $this->request->get();
        $data = PackageModel::getLists($params);
        $this->success('课程包列表', $data);
    }

    /**
     * 课程包详情
     */
    public function detail()
    {
        $params = $this->request->get();
        $detail = PackageModel::getDetail($params);
        if(!$detail){
            $this->error('课程包不存在！');
        }
        $this->success('课程包详情', $detail);
    }

	
}