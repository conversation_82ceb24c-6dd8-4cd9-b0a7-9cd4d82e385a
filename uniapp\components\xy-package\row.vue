<template>
	<view class="xy-package-row">
		<view class="item flex" :key="index" @tap="$xyfun.to('/pages/package/detail?id='+item.id)">
			<view class="l"><image :src="$xyfun.image(item.thumbimage)" class="br-10" /></view>
			<view class="r m-l-20">
				<view class="m-t-10 tb">
					{{item.name}}
				</view>
				<view class="ts-36 tb m-t-15" :style="css.tcp">
					<text class="ts-28">¥</text>{{item.price}}
				</view>
				<view class="flex m-t-15 lh-54">
					<view :style="css.tcl" class="ts-26" v-if="sales">已售 {{$xyfun.bcadd(item.sales,item.virtualsales)}}</view>
					<view class="buy m-l-auto tc-w tc ts-26" :style="css.mcbg" v-if="buy">购买</view>
					<view class="m-l-auto ts-26" :style="css.tcl" v-else>有效期：<text :style="css.tcm">{{item.limitday}}</text>天</view>
				</view>
				
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: "xyPackageRow",
		props: {
			item: {
				type: Object,
			},
			sales:{
				type:Boolean,
				default: true
			},
			buy:{
				type:Boolean,
				default:true
			}
		},
		data() {
			return {
				css:this.$xyfun.css(),
			};
		},
		methods: {
			
		}
	}
</script>
<style lang="scss">
	.xy-package-row{
		.item{
			flex-wrap: nowrap;
			width: 100%;
			.l,.l image{width: 240rpx;height: 180rpx;}
			.r{
				width: 100%;
				.buy{width: 130rpx;height: 54rpx;line-height: 54rpx;border-radius: 27rpx;}
			}
		}
	}
</style>
