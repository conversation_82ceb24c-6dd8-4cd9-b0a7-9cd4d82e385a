<?php

namespace app\admin\controller\xycourse\distribution;

use app\common\controller\Backend;

/**
 * 分销订单管理
 *
 * @icon fa fa-circle-o
 */
class Order extends Backend
{

    /**
     * Order模型对象
     * @var \app\admin\model\xycourse\distribution\Order
     */
    protected $model = null;
    protected $searchFields = 'order_sn';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xycourse\distribution\Order;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index($distribution_id = 0)
    {
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();
       
        $where1 = [];
        if($distribution_id>0){
            $where1 = ['one_distribution_id|two_distribution_id'=>$distribution_id];
        }

        $total = $this->model
                ->with(['buyer','one','two'])
                ->where($where)
                ->where($where1)
                ->order($sort, $order)
                ->count();

        $list = $this->model
                ->with(['buyer','one','two'])
                ->where($where)
                ->where($where1)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

        foreach ($list as $row) {
            $row->order_info = $this->getOrderInfo($row);
        }
        $list = collection($list)->toArray();
        $result = array("total" => $total, "rows" => $list);


        return json($result);
    }

    /**
     * 获取分销订单详情
     */
    private function getOrderInfo($row){
        $orderInfo = null;
        $type = $row['order_type'];
        $orderId = $row['service_order_id'];
        if($type == 'order'){
            $orderInfo = (new \app\admin\model\xycourse\order\Order())->where(['id'=>$orderId])->find();
        }
        
        return $orderInfo;
    }

    //添加
    public function add() {
        return;
    }
    //编辑
    public function edit($ids = null) {
        return;
    }
    //删除
    public function del($ids = null) {
        return;
    }
    //回收站列表
    public function recyclebin() {
        return;
    }
    //回收站(真实删除或清空)
    public function destroy($ids = null) {
        return;
    }
    //回收站还原
    public function restore($ids = null) {
        return;
    }
    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }
}
