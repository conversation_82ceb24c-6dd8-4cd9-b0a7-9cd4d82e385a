<?php

namespace app\admin\controller\xycourse;
use app\common\controller\Backend;
use app\api\model\xycourse\user\Course as UserCourseModel;
use app\admin\model\xycourse\Scheduling;
use Exception;
use think\Db;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\response\Json;

/**
 * 预约管理
 *
 * @icon fa fa-circle-o
 */
class Appointment extends Backend
{

    /**
     * Appointment模型对象
     * @var \app\admin\model\xycourse\Appointment
     */
    protected $model = null;
    protected $searchFields = 'id,user.nickname,user.mobile,coursename';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xycourse\Appointment;
        $this->view->assign("typeList", $this->model->getTypeList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 消课
     */
    public function verify($ids = null)
    {
        $appointment = $this->model->get($ids);
       
        if (!$appointment || $appointment['status'] != 1) {
            new Exception('预约不存在或已取消');
        }

        $appointment->status = 2;
        $appointment->ext = json_encode($appointment->setExt($appointment, ['verify_time' => time()])); 
        $appointment->save();
        

        $this->success();
    }

   
    public function cancel($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            
            $row->status = -1;
            $row->ext = json_encode($row->setExt($row, ['cancel_time' => time(),'cancel_reason'=>'后台取消']));
            $row->save();
    
            //返回次数
            $userCourseInfo = UserCourseModel::get($row['user_course_id']);
            UserCourseModel::change($userCourseInfo['user_package_id'],$row['course_id'],$row['user_id'],1,'cancel_appointment',$this->auth->id,'',$userCourseInfo['id']);
    
            // 排课信息
            $schedulingInfo = Scheduling::where(['id'=>$row['scheduling_id']])->find();
            if(!empty($schedulingInfo)){
                $schedulingInfo->setDec('agreed');
            }
            
            $result = true;

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }



    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index()
    {
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();
        $list = $this->model
            ->with(['coach','user','course'])
            ->where($where)
            ->order($sort, $order)
            ->paginate($limit);

        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 详情
     *
     * @param $ids
     */
    public function detail($id = null)
    {
        $row = $this->model->with(['user','coach','course'])->where(['appointment.id'=>$id])->find();
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        $this->view->assign('row', $row);
        return $this->view->fetch();
    }


    //添加
    public function add() {
        return;
    }
    //编辑
    public function edit($ids = null) {
        return;
    }
    //删除
    public function del($ids = null) {
        return;
    }
    
    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }


}
