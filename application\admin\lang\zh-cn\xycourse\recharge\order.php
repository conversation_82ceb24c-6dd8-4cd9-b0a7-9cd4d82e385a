<?php

return [
    'Id'                         => 'ID',
    'Order_sn'                   => '订单号',
    'User_id'                    => '用户',
    'Money'                      => '充值金额',
    'Total_fee'                  => '支付金额',
    'Pay_fee'                    => '实际支付金额',
    'Transaction_id'             => '交易单号',
    'Payment_json'               => '交易原始数据',
    'Paytime'                    => '支付时间',
    'Ext'                        => '附加字段',
    'Platform'                   => '平台',
    'Platform wxminiprogram'     => '微信小程序',
    'Platform wxofficialaccount' => '微信公众号',
    'Status'                     => '订单状态',
    'Status -2'                  => '交易关闭',
    'Set status to -2'           => '设为交易关闭',
    'Status -1'                  => '已取消',
    'Set status to -1'           => '设为已取消',
    'Status 0'                   => '未支付',
    'Set status to 0'            => '设为未支付',
    'Status 1'                   => '已支付',
    'Set status to 1'            => '设为已支付',
    'Createtime'                 => '创建时间',
    'Updatetime'                 => '更新时间'
];
