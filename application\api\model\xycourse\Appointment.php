<?php

namespace app\api\model\xycourse;
use app\api\model\xycourse\user\User;
use app\api\model\xycourse\Scheduling;
use addons\xycourse\exception\Exception;
use app\api\model\xycourse\store\Store;
use app\api\model\xycourse\Course;
use app\api\model\xycourse\user\Course as UserCourseModel;
use app\api\model\xycourse\coach\Coach;
use think\Model;
use think\Db;


class Appointment extends Model
{

    // 表名
    protected $name = 'xycourse_appointment';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_text',
        'status_text',
        'ext_arr'
    ];

    public function getExtArrAttr($value, $data)
    {
        $ext = (isset($data['ext']) && $data['ext']) ? json_decode($data['ext'], true) : [];
        return $ext;
    }

    public static function getLists($params)
    {
        extract($params);

        if(isset($coach_id) && $coach_id > 0){
            $where['coach_id'] = $coach_id;
        }else if(isset($user_id) && $user_id > 0){
            $where['user_id'] = $user_id;
        }else if(isset($staff_id) && $staff_id > 0){
            $where['user_id'] = ['>',0];
        }else{
            $user = User::info();
            $where['user_id'] = $user->id;
        }

        
        if(isset($date) && $date  != ''){
            $where['date'] = $date;
        }
       
        if($status != 'all'){
            $where['status'] = $status;
        }
        $list = self::where($where)->order('id desc')->paginate();

        return $list;
    }

    /**
     * 创建预约
     */
    public static function addAppointment($params)
    {
        $user = User::info();
        extract($params);

        // 排课信息
        $schedulingInfo = Scheduling::where(['id'=>$scheduling_id])->find();
        if(empty($schedulingInfo)){
            new Exception('排课信息错误，无法预约');
        }

        if($schedulingInfo['agreed'] >= $schedulingInfo['people']){
            new Exception('预约已满，无法预约');
        }

        if($schedulingInfo->starttime < time()){
            new Exception('时间已过,不能预约');
        }

        // 会员课程
        $userCourseInfo = UserCourseModel::where(['user_id'=>$user->id,'course_id'=>$schedulingInfo['course_id'],'residue'=>['>',0]])->whereTime('duedate', '>=',$schedulingInfo['date'])->order('duedate asc')->find();

        //var_dump($userCourseInfo);

        
        if(empty($userCourseInfo)){
            new Exception('您没有购买此课程、或课程已过期无法预约');
        }else if($userCourseInfo['residue'] < 1){
            new Exception('您的课程已上完，无法预约');
        }

        // 课程信息
        $courseInfo = Course::where(['id'=>$schedulingInfo['course_id']])->find();
        if(empty($courseInfo)){
            new Exception('课程已下架');
        }

        // 老师信息
        $coachInfo = Coach::where(['id'=>$schedulingInfo['coach_id'],'status'=>'normal'])->find();
        if(empty($coachInfo)){
            new Exception('老师已离职');
        }

        // 检测是否已约
        $appointmentInfo = self::where(['user_id'=>$user->id,'coach_id'=>$coachInfo ? $coachInfo->id : 0,'scheduling_id'=>$schedulingInfo->id,'status'=>1])->find();
        if(!empty($appointmentInfo)){
            new Exception('请不要重复预约');
        }

        $appointment = Db::transaction(function () use ($user,$schedulingInfo,$courseInfo,$coachInfo,$userCourseInfo) {
            
            // 预约信息
            $appointmentData = [];
            $appointmentData['user_id'] = $user->id;
            $appointmentData['coach_id'] = $coachInfo ? $coachInfo->id : 0;
            $appointmentData['course_id'] = $courseInfo->id;
            $appointmentData['date'] = $schedulingInfo->date;
            $appointmentData['scheduling_id'] = $schedulingInfo->id;
            $appointmentData['user_course_id'] = $userCourseInfo->id;
            $appointmentData['timestr'] = $schedulingInfo->timestr;
            $appointmentData['starttime'] = $schedulingInfo->starttime;
            $appointmentData['endtime'] = $schedulingInfo->endtime;
            $appointmentData['type'] = $schedulingInfo['type'];
            $appointmentData['coachrealname'] = $coachInfo ? $coachInfo->realname : '';
            $appointmentData['coachheadimage'] = $coachInfo ? $coachInfo->headimage : '';
            $appointmentData['coursename'] = $courseInfo->name;
            $appointmentData['coursethumbimage'] = $courseInfo->thumbimage;
            $appointmentData['operate_user_id'] = $user->id;
            $appointment = new Appointment();

            $appointment->allowField(true)->save($appointmentData);

            //更改会员课程
            UserCourseModel::change($userCourseInfo['user_package_id'],$courseInfo->id,$user->id,-1,'appointment',$user->id,'',$userCourseInfo['id']);

            //更改排班预约人数
            $schedulingInfo->setInc('agreed');

            return $appointment;
        });


        return $appointment;
    }
    
    // 取消预约
    public static function cancelAppointment($params)
    {
        extract($params);

        $coach_id = isset($coach_id) ? $coach_id : 0;
        $cancel_user_id = $coach_id ? $coach_id : $user_id;
        $cancelReason = "您自己取消了预约";

        if($coach_id){
            $cancelReason = '老师取消了预约';
        }

        $appointment = self::where(['user_id'=>$user_id,'id'=>$id,'status'=>1])->find($id);
        if (!$appointment) {
            new Exception('预约不存在或已取消');
        }

        if(time() > $appointment['starttime'] && $coach_id == 0){
            new Exception('已超过开始上课时间不能取消');
        }

        $appointment->status = -1;
        $appointment->cancel_user_id = $cancel_user_id;
        $appointment->ext = json_encode($appointment->setExt($appointment, ['cancel_time' => time(),'cancel_reason'=>$cancelReason]));
        $appointment->save();

        // 返回次数
        $userCourseInfo = UserCourseModel::get($appointment['user_course_id']);
        UserCourseModel::change($userCourseInfo['user_package_id'],$appointment['course_id'],$user_id,1,'cancel_appointment',$coach_id ? $coach_id : $user_id,'',$userCourseInfo['id'],'',$userCourseInfo['id']);

        // 排课信息
        $schedulingInfo = Scheduling::where(['id'=>$appointment['scheduling_id']])->find();
        if(!empty($schedulingInfo)){
            $schedulingInfo->setDec('agreed');
        }

        return $appointment;
    }

    // 完成预约
    public static function verifyAppointment($params)
    {
        extract($params);

        $coach_id = isset($coach_id) ? $coach_id : 0;

        $appointment = self::where(['user_id'=>$user_id,'id'=>$id,'status'=>1])->find($id);
        if (!$appointment) {
            new Exception('预约不存在或已取消');
        }

        if(time() > $appointment['starttime']){
            new Exception('已超过开始上课时间不能取消');
        }

        $appointment->status = 2;
        $appointment->verify_user_id = $coach_id;
        $appointment->ext = json_encode($appointment->setExt($appointment, ['verify_time' => time()])); 
        $appointment->save();
        
        return $appointment;
    }

    public function setExt($appointment, $field, $origin = [])
    {
        $newExt = array_merge($origin, $field);

        $orderExt = $appointment['ext_arr'];

        return array_merge($orderExt, $newExt);
    }
    
    public function getTypeList()
    {
        return ['league' => __('团课'), 'private' => __('私教')];
    }

    public function getStatusList()
    {
        return ['1' => __('待上课'), '2' => __('已完成'),'-1' => __('已取消')];
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    


}
