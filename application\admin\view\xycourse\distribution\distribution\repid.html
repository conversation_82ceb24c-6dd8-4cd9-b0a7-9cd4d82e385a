<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div id="app" v-clock>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('上级会员')}:</label>
            <div class="col-xs-12 col-sm-8">
                <a @click="selectUser()" class="btn btn-select btn-success"><i class="fa fa-bars"></i> 选择会员 </a>
                <input id="c-pid" data-rule="required" name="row[pid]" type="hidden" v-model="userInfo.id">
            </div>
        </div>
        <div class="form-group" v-if="userInfo.id">
            <label class="control-label col-xs-12 col-sm-2"></label>
            <div class="col-xs-12 col-sm-8">
                <div style="display: flex;">
                    <img :src="userInfo.avatar" width="50px" height="50px" />
                    <div style="margin: 0 30px 0px 10px;" >
                        <p>{{ userInfo.nickname }}</p>
                        <p>{{ userInfo.mobile }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
   
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>

</form>

<style>
    [v-clock] {display: none;}
    .btn-select{height: 34px;line-height: 34px;padding: 0 20px;}
</style>
