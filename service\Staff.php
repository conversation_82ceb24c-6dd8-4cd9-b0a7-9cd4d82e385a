<?php

namespace addons\xycourse\service;

use app\api\model\xycourse\user\User as UserModel;
use app\api\model\xycourse\Staff as StaffModel;
use app\api\model\xycourse\store\Store as StoreModel;

/**
 * 员工业务
 */
class Staff
{

    public $user;     // 用户
    public $staff;    // 员工
    public $store;   // 门店

    // 员工状态
    const COACH_STATUS_NORMAL = 'normal';       // 正常 
    const COACH_STATUS_NULL = NULL;             // 未成为员工
    const COACH_STATUS_HIDDEN = 'hidden';       // 离职

    public function __construct($user_id)
    {
        $this->user = UserModel::where('id', $user_id)->field('id, nickname, username,mobile')->find();
        $this->store = StoreModel::get(1);
        $this->staff = StaffModel::where(['user_id'=>$user_id])->find();
    }

    // 获取员工状态
    public function getStaffStatus()
    {
        $staffStatus = 'normal';

        if (empty($this->staff)) {
            $staffStatus = self::COACH_STATUS_NULL;
        }else{
            $staffStatus = $this->staff->status;
        }

        $response = [
            'status' => $staffStatus,
            'msg'    => ''
        ];

        switch ($staffStatus) {
            case self::COACH_STATUS_NULL:
                $response['msg'] = '您不是门店的员工';
                break;
            case self::COACH_STATUS_HIDDEN:
                $response['msg'] = '您已离职';
                break;
        }
        return $response;
    }
    
    
}
