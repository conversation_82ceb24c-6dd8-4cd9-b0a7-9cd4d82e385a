define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'xycourse_vue'], function ($, undefined, Backend, Table, Form, Vue) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xycourse/scheduling/index' + location.search,
                    add_url: 'xycourse/scheduling/add',
                    del_url: 'xycourse/scheduling/del',
                    multi_url: 'xycourse/scheduling/multi',
                    import_url: 'xycourse/scheduling/import',
                    table: 'xycourse_scheduling',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'coach.realname', title: __('Coach_id')},
                        {field: 'course.name', title: __('Course_id')},
                        {field: 'type', title: __('Type'), searchList: {"league":__('Type league'),"private":__('Type private')}, formatter: Table.api.formatter.normal},
                        {field: 'date', title: __('Date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'timestr', title: __('Timestr'), operate: 'LIKE'},
                        {field: 'people', title: __('People')},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            //修改默认弹窗大小
            Fast.config.openArea = ['80%', '90%'];

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            new Vue({
                el: "#app",
                data() {
                    return {
                        coachInfo:'',// 老师
                        courseInfo:'',// 课程
                        days:1, // 排班天数
                        weeks:[{value:1,name:'周一'},{value:2,name:'周二'},{value:3,name:'周三'},{value:4,name:'周四'},{value:5,name:'周五'},{value:1,name:'周一'}]
                    }
                },
                methods: {

                    selectCoach(){
                        var that = this;
                        parent.Fast.api.open("xycourse/coach/coach/select?multiple=false", __('选择老师'), {
                            area: ['80%', '80%'],
                            callback: function (data) {
                                that.coachInfo = data;
                            }
                        });
                    },

                    selectCourse(){
                        var that = this;
                        parent.Fast.api.open("xycourse/course/select?multiple=false", __('选择课程'), {
                            area: ['80%', '80%'],
                            callback: function (data) {
                                that.courseInfo = data;
                            }
                        });
                    },

                    //更改排班天数
                    changeDays(){
                        this.days = parseInt($("#c-days").val());
                        if(this.storeInfo != ''){
                            this.setTimeprice();
                            this.setHolidayprice();
                        }
                    },

                },
            })

            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
