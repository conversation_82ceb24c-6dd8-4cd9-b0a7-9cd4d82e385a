<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;

use app\api\model\xycourse\Article as ArticleModel;

/**
 * XYcourse文章接口
 */
class Article extends Api
{
    protected $noNeedLogin = ['lists','detail'];
    protected $noNeedRight = ['*'];
    
	
    /**
     * 文章详情
     */
    public function detail()
    {
        $id = $this->request->get('id');
        $detail = ArticleModel::getDetail($id);

        if(!$detail){
            $this->error('文章不存在！');
        }

        $this->success('文章详情', $detail);
    }
	
}