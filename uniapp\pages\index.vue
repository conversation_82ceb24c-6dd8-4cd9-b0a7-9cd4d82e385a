<template>
	<view class="index p-b-40" :style="pageStyle+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		<view v-for="(item, index) in itemList" :key="index">
			<!-- 轮播 -->
			<xy-banner v-if="item.type == 'banner'" :data="item" />
			<!-- 菜单 -->
			<xy-menu v-if="item.type == 'menu'" :data="item" />
			<!-- 图片 -->
			<xy-image v-if="item.type == 'image'" :data="item" />
			<!-- 标题 -->
			<xy-title v-if="item.type == 'title'" :data="item" />
			<!-- 课程包 -->
			<xy-package v-if="item.type == 'package'" :data="item" />
			<!-- 门店 -->
			<xy-store v-if="item.type == 'store'" :data="item" /> 
			<!-- 空白行 --> 
			<view v-if="item.type == 'empty'" :style="'height:'+item.params.height+'rpx'" />
		</view>
		<xy-tabbar />
	</view>
</template>
<script>
	import xyBanner from '@/components/xy-banner';
	import xyMenu from '@/components/xy-menu';
	import xyImage from '@/components/xy-image';
	import xyTitle from '@/components/xy-title';
	import xyTabbar from '@/components/xy-tabbar';
	import xyPackage from '@/components/xy-package';
	import xyStore from '@/components/xy-store';
	export default {
		components: {
			xyTabbar,
			xyBanner,
			xyMenu,
			xyImage,
			xyTitle,
			xyPackage,
			xyStore
		},
		data() {
			return {
				css: {},
				isLoading:true,
				itemList:[],
				pageStyle:{}
				
			}
		},
		onPullDownRefresh() {
			this.loadData()
		},
		onLoad() {
			this.css = this.$xyfun.css();
			this.loadData();
		},
		methods: {
			loadData(){
				this.$api.get({
					url: '/common/template',
					loadingTip:'加载中...',
					data: {
						type:'index',
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.isLoading = false;
						this.itemList = res.item;
						this.pageStyle = this.$xyfun.tcss(res.page)
						this.$xyfun.setNavBg(res.page.style.navigationBarBackgroundColor,res.page.style.navigationBarTextStyle);
						this.$xyfun.setNavTitle(res.page.params.navigationBarTitleText);
					}
				});
			},
		}
	}


</script>

<style scoped lang="scss">
</style>