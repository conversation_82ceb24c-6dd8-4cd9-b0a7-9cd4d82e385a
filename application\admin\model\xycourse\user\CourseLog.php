<?php

namespace app\admin\model\xycourse\user;

use think\Model;


class CourseLog extends Model
{

    

    

    // 表名
    protected $name = 'xycourse_user_course_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    public function coach()
    {
        return $this->belongsTo('\app\admin\model\xycourse\coach\Coach', 'operate_user_id', 'user_id', [], 'LEFT')->setEagerlyType(0);
    }

    public function user()
    {
        return $this->belongsTo('\app\common\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function course()
    {
        return $this->belongsTo('\app\admin\model\xycourse\user\Course', 'user_course_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }





}
