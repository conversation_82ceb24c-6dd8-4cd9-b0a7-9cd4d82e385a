<?php

namespace app\api\controller\xycourse;
use app\api\model\xycourse\Config;
use addons\xycourse\library\Decrypt\weixin\wxBizDataCrypt;
use app\common\controller\Api;
use app\api\model\xycourse\user\User as UserModel;
use fast\Random;
use fast\Http;

/**
 * XYcourse会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['phone', 'captcha', 'register', 'login', 'passwordRegister', 'passwordLogin'];
    protected $noNeedRight = ['*'];
    
    public function _initialize()
    {
        parent::_initialize();
		$this->auth->setAllowFields(['id','username','nickname','mobile','avatar','level','xycourse_parent_user_id','xycourse_consume','xycourse_recharge','gender','birthday','bio','money','score','successions','maxsuccessions','prevtime','logintime','loginip','jointime','status']);
    }
	

	/**
     * 退出登录
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }
	
    
    /**
     * 手机号授权登录
     */
    public function phone()
    {
        //设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$post = $this->request->post();

			

			$platform = request()->header('platform');
		    if (!isset($post['iv'])) {
		        $this->error(__('获取手机号异常'));
		    }
		    // 获取配置
		    $config = Config::getValueByName('wxminiprogram');

			if(empty($config['app_id']) || empty($config['secret'])){
				$this->error(__('请到后台配置中心平台配置中配置微信小程序AppID和AppSecret'));
			}

	        $params = [
			    'appid'    => $config['app_id'],
			    'secret'   => $config['secret'],
			    'js_code'  => $post['code'],
			    'grant_type' => 'authorization_code'
			];
			
			$ret = \think\Db::transaction(function () use ($params,$platform,$post) {
				$isInfo = 0;
				try {
					
					$result = Http::sendRequest("https://api.weixin.qq.com/sns/jscode2session", $params, 'GET');
					$json = (array)json_decode($result['msg'], true);

					// 判断third是否存在,存在快速登录
					$third = model('app\api\model\xycourse\Third')->get(['platform' => $platform, 'openid' => $json['openid']]);

					// 手机号解码
					$encryptedData = request()->post('encryptedData','','trim');
					$decrypt = new wxBizDataCrypt($params['appid'], $json['session_key']);
					$decrypt->decryptData($encryptedData, $post['iv'], $data);
					$data = (array)json_decode($data, true);
					
					if ($third && $third['user_id'] != 0) {
						//如果已经有账号则直接登录
						$isInfo = 2;
						$third->save([
							'openid' => $json['openid'],
							'logintime' => time(),
						]);

						// 开始登录
						$user = UserModel::where(['id'=>$third['user_id']])->find();

						if(!empty($user) && $user->status != 'normal'){
							$this->error(__('账号已被禁用'));
						}
						
						if(!empty($user) && $user['mobile'] == ''){
							$user->mobile = $data['phoneNumber'];
							$user->save();
						}

						$this->auth->direct($third['user_id']);
						

					} else {
						
						// 开始登录
						$mobile = $data['phoneNumber'];
						$user = \app\common\model\User::getByMobile($mobile);
						if ($user) {
							if ($user->status != 'normal') {
								$this->error(__('账号已被禁用'));
							}
							//如果已经有账号则直接登录
							$isInfo = 2;
							$this->auth->direct($user->id);
						} else {
							$isInfo = 1;
							$this->auth->register('U'.Random::alnum(6), Random::alnum(), '', $mobile, [
								'nickname' => 'U-'.Random::nozero(6), 
								'avatar' => Config::getValueByName('xycourse')['useravatar']?Config::getValueByName('xycourse')['useravatar']:''
							]);
						}

						// 新增$third
						$third = model('app\api\model\xycourse\Third');
						$third->platform  = $platform;
						$third->openid  = $json['openid'];
						$third->session_key  = $json['session_key'];
						$third->logintime  = time();
						$third->user_id  = $this->auth->id;
						$third->save();
						
					}
					return $isInfo;
				} catch (\Exception $e) {
					$this->error($e->getMessage());
				}
				return $isInfo;
			});	
			
		    if ($ret) {
    			$this->success(__('Logged in successful'), self::userInfo($ret));
    		} else {
    			$this->error($this->auth->getError());
    		}
		}
		$this->error(__('非法请求'));
    }

	/**
	 * 刷新用户信息
	 */
	public function refresh()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if ($this->request->isPost()) {
			$this->success(__('刷新成功'), self::userInfo());
		}
		$this->error(__('非法请求'));
	}

	/**
     * 修改信息
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $nickname = $this->request->post('nickname');
        $avatar = $this->request->post('avatar');
		$mobile = $this->request->post('mobile');
		
		if(!empty($mobile) && $mobile != $user['mobile']){
			$userE = UserModel::where(['mobile'=>$mobile])->find();
			if(!empty($userE)){
				$this->error(__('手机号已存在'));
			}
			$user->mobile = $mobile;
		}
        
        $user->nickname = $nickname;
		
        if (!empty($avatar)) {
            $user->avatar = $avatar;
        }
        $user->save();
        $this->success('修改成功',self::userInfo());
    }
	

	/**
	 * 登录后返回用户信息
	 */
	private function userInfo($isInfo = 0)
	{
		// 获取配置
		$userInfo = $this->auth->getUserinfo();
		$userInfo['is_info'] = $isInfo;
		return [
			'userInfo' => $userInfo,
		];
	}

	/**
	 * 获取验证码
	 */
	public function captcha() {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if (!$this->request->isPost()) {
			$this->error(__('非法请求'));
		}

		$mobile = $this->request->post('mobile');
		if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
			$this->error('无效手机号');
		}

		// Generate captcha, e.g., random 6 digits
		$captcha = rand(100000, 999999);
		// Store in cache with mobile as key, expire in 5 min
		\think\Cache::set('captcha_' . $mobile, $captcha, 300);

		// TODO: Send SMS via service
		// For demo, just log it
		trace('Captcha for ' . $mobile . ': ' . $captcha);
		$this->success('验证码已发送');
	}

	/**
	 * 验证码注册
	 */
	public function register() {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if (!$this->request->isPost()) {
			$this->error(__('非法请求'));
		}

		$mobile = $this->request->post('mobile');
		$captcha = $this->request->post('captcha');

		if (!$mobile || !$captcha) {
			$this->error('请输入手机号和验证码');
		}

		$stored = \think\Cache::get('captcha_' . $mobile);
		if ($stored != $captcha) {
			$this->error('验证码错误');
		}

		$user = \app\common\model\User::getByMobile($mobile);
		if ($user) {
			$this->error('用户已存在');
		}

		$this->auth->register('U'.\fast\Random::alnum(6), \fast\Random::alnum(), '', $mobile, [
			'nickname' => 'U-'.\fast\Random::nozero(6),
			'avatar' => Config::getValueByName('xycourse')['useravatar'] ?: ''
		]);

		$this->success('注册成功', $this->userInfo(1));
	}

	/**
	 * 验证码登录
	 */
	public function login() {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if (!$this->request->isPost()) {
			$this->error(__('非法请求'));
		}

		$mobile = $this->request->post('mobile');
		$captcha = $this->request->post('captcha');

		if (!$mobile || !$captcha) {
			$this->error('请输入手机号和验证码');
		}

		$stored = \think\Cache::get('captcha_' . $mobile);
		if ($stored != $captcha) {
			$this->error('验证码错误');
		}

		$user = \app\common\model\User::getByMobile($mobile);
		if (!$user) {
			$this->error('用户不存在');
		}

		if ($user->status != 'normal') {
			$this->error('账号已被禁用');
		}

		$this->auth->direct($user->id);
		$this->success('登录成功', $this->userInfo(2));
	}
	/**
	 * 密码注册
	 */
	public function passwordRegister() {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if (!$this->request->isPost()) {
			$this->error(__('非法请求'));
		}

		$username = $this->request->post('username');
		$password = $this->request->post('password');
		$mobile = $this->request->post('mobile', '');

		if (!$username || !$password) {
			$this->error('请输入用户名和密码');
		}

		if (strlen($password) < 6) {
			$this->error('密码长度不能少于6位');
		}

		if (\app\common\model\User::getByUsername($username)) {
			$this->error('用户名已存在');
		}

		if ($mobile && \app\common\model\User::getByMobile($mobile)) {
			$this->error('手机号已存在');
		}

		$this->auth->register($username, $password, '', $mobile, [
			'nickname' => $username,
			'avatar' => Config::getValueByName('xycourse')['useravatar'] ?: ''
		]);

		$this->success('注册成功', $this->userInfo(1));
	}

	/**
	 * 密码登录
	 */
	public function passwordLogin() {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if (!$this->request->isPost()) {
			$this->error(__('非法请求'));
		}

		$username = $this->request->post('username');
		$password = $this->request->post('password');

		if (!$username || !$password) {
			$this->error('请输入用户名和密码');
		}

		$ret = $this->auth->login($username, $password);
		if ($ret) {
			$this->success('登录成功', $this->userInfo(2));
		} else {
			$this->error($this->auth->getError());
		}
	}
}
