<?php

namespace app\admin\controller\xycourse;

use app\common\controller\Backend;
use Exception;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
/**
 * 排班
 *
 * @icon fa fa-circle-o
 */
class Scheduling extends Backend
{

    /**
     * Scheduling模型对象
     * @var \app\admin\model\xycourse\Scheduling
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xycourse\Scheduling;
        $this->view->assign("daysList", $this->model->getDaysList());
        $this->view->assign("weeksList", $this->model->getWeeksList());
        $this->view->assign("typeList", $this->model->getTypeList());
    }

    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index()
    {
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();
        $list = $this->model
            ->with(['coach','course'])
            ->where($where)
            ->order($sort, $order)
            ->paginate($limit);

        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        
        if (empty($params['coach_id'])) {
            $this->error(__('请选择老师', ''));
        }
        if (empty($params['course_id'])) {
            $this->error(__('请选择课程', ''));
        }
        if($params['people'] < 1){
            $this->error(__('预约人数不能小于1', ''));
        }

        if($params['days'] > 1 && $params['weeks'][0] == ''){
            $this->error(__('请选择周几', ''));
        }

        $date = $params['date'];
        $data =[];
        for($i=1;$i<=intval($params['days']);$i++){

            $weekday = date('w', strtotime($date));
            if ($params['days'] == 1 || in_array($weekday, $params['weeks'])) {
                $startTime = strtotime($date.$params['starttime']);
                $endTime = strtotime($date.$params['endtime']);

                if($startTime >= $endTime){
                    $this->error(__('时段选择错误', ''));
                }

                
                $scheduling = $this->model->where(['course_id'=>$params['course_id'],'starttime|endtime'=>['between',[$startTime+1,$endTime-1]]])->find();

                if (!empty($scheduling)) {
                    $this->error(__('该时段与别的时间段重叠了,请重新选', ''));
                }

                array_push($data,[
                    'coach_id'  => $params['coach_id'],
                    'course_id' => $params['course_id'],
                    'type'      => $params['type'],
                    'date'      => $date,
                    'starttime' => $startTime,
                    'endtime'   => $endTime,
                    'timestr'   => $params['starttime'].' - '.$params['endtime'],
                    'people'    => $params['people']
                ]);
            }

            $date = date("Y-m-d",strtotime("+1 day",strtotime($date)));
        }

        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            
            $result = $this->model->allowField(true)->saveAll($data);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    //编辑
    public function edit($ids = null) {
        return;
    }

    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }


}
