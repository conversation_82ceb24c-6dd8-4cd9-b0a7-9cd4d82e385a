<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div id="app" v-clock>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Coach_id')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="select-data">
                    <input type="hidden" class="form-control" name="row[coach_id]" v-model="coachInfo.id">
                    <input type="text" readonly class="form-control" v-model="coachInfo.realname" placeholder="点击右侧按钮选择老师">
                    <div @click="selectCoach()" class="btn btn-success"><i class="fa fa-bars"></i> 选择老师 </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Course_id')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="select-data">
                    <input type="hidden" class="form-control" name="row[course_id]" v-model="courseInfo.id">
                    <input type="hidden" class="form-control" name="row[type]" v-model="courseInfo.type">
                    <input type="text" readonly class="form-control" :value="courseInfo ? courseInfo.name + ' 【'+courseInfo.type_text+'】' : ''" placeholder="点击右侧按钮选择老师">
                    <div @click="selectCourse()" class="btn btn-success"><i class="fa fa-bars"></i> 选择课程 </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('开始日期')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[date]" type="text" value="{:date('Y-m-d')}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('时段')}:</label>
            <div class="col-xs-12 col-sm-8" style="display: flex;">
                <input id="c-starttime" data-rule="required" class="form-control datetimepicker" name="row[starttime]" data-date-format="HH:ss" type="text" value="" style="width: 45%;">
                <div class="control-label" style="width:10%;text-align: center;">-</div>
                <input id="c-endtime" data-rule="required" class="form-control datetimepicker" name="row[endtime]" data-date-format="HH:ss" type="text" value="" style="width: 45%;">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('People')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-people" data-rule="required" class="form-control" name="row[people]" type="number" value="1">
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('排班天数')}:</label>
            <div class="col-xs-12 col-sm-8">
                            
                <select  id="c-days" class="form-control selectpicker" name="row[days]" @change="changeDays()">
                    {foreach name="daysList" item="vo"}
                        <option value="{$key|htmlentities}" {in name="key" value="60"}selected{/in}>{$vo|htmlentities}</option>
                    {/foreach}
                </select>
    
            </div>
        </div>

        <div class="form-group" v-if="days>1">
            <label class="control-label col-xs-12 col-sm-2">{:__('选择周几')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="checkbox">
                {foreach name="weeksList" item="vo"}
                <label for="row[weeks][]-{$key|htmlentities}"><input id="row[weeks][]-{$key|htmlentities}" name="row[weeks][]" type="checkbox" value="{$key|htmlentities}" /> {$vo|htmlentities}</label> 
                {/foreach}
                </div>
            </div>
        </div>

    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
    [v-clock] {display: none;}
    .select-data{display: flex;}
</style>
