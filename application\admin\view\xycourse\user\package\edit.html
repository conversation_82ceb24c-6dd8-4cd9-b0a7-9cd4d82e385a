<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$row.store_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Package_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-package_id" data-rule="required" data-source="package/index" class="form-control selectpage" name="row[package_id]" type="text" value="{$row.package_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Packagename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-packagename" data-rule="required" class="form-control" name="row[packagename]" type="text" value="{$row.packagename|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Packagethumbimage')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-packagethumbimage" data-rule="required" class="form-control" size="50" name="row[packagethumbimage]" type="text" value="{$row.packagethumbimage|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-packagethumbimage" class="btn btn-danger faupload" data-input-id="c-packagethumbimage" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-packagethumbimage"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-packagethumbimage" class="btn btn-primary fachoose" data-input-id="c-packagethumbimage" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-packagethumbimage"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-packagethumbimage"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Duedate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-duedate" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[duedate]" type="text" value="{$row.duedate|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
