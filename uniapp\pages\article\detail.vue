<template>
	<view class="article-detail" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!empty">
		<view v-if="article.image != null">
			<image :src="$xyfun.image(article.image)" mode="widthFix" style="width: 100%;" @tap="$xyfun.pi(0,[article.image])"></image>
		</view>
		<view class="content p-30">
			<rich-text :nodes="article.content"></rich-text>
		</view>
	</view>
</template>

<script>
	
	export default {
		
		data() {
			return {
				css:{},
				empty:true,
				id: 103,
				article:{},
			}
		},
		onLoad() {
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		methods: {
			
			loadData(){
				this.$api.get({
					url: '/article/detail',
					loadingTip:'加载中...',
					data: {
						id:this.$Route.query.id,
					},
					success: res => {
						this.empty = false;
						this.article = res;
						this.article.content = this.article.content.replace(/\<img/g, "<img style='max-width: 100%;vertical-align: middle;'");
						this.$xyfun.setNavTitle(res.title);
					}
				});
			},
			
			
			
		}
	}
</script>

<style scoped lang="scss">
	.article-detail{
		.content{text-align: justify;}
	}
</style>
