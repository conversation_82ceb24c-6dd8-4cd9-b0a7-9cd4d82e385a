/**
 * 
 * XYcourse
 * <AUTHOR>
 * 
 **/

import App from './App';
import Vue from 'vue';
import {router,RouterMount} from './router/index.js'  //路由
Vue.use(router)

import store from './store';
import xyfun from "./utils/xyfun";
import api from './utils/request';

import mixin from "./utils/mixin"
Vue.mixin(mixin)

Vue.prototype.$xyfun = xyfun;  // 通用方法
Vue.prototype.$api = api;      // api请求
Vue.prototype.$store = store;  // store

App.mpType = 'app'

const app = new Vue({
	store,
    ...App
})

// #ifdef H5
	RouterMount(app,router,'#app')
// #endif
 
// #ifndef H5
	app.$mount();
// #endif