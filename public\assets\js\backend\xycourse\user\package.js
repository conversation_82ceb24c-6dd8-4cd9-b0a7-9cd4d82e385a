define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xycourse/user/package/index' + location.search,
                    add_url: 'xycourse/user/package/add',
                    edit_url: 'xycourse/user/package/edit',
                    del_url: 'xycourse/user/package/del',
                    multi_url: 'xycourse/user/package/multi',
                    import_url: 'xycourse/user/package/import',
                    table: 'xycourse_user_package',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user_id', title: __('User_id')},
                        {field: 'store_id', title: __('Store_id')},
                        {field: 'package_id', title: __('Package_id')},
                        {field: 'packagename', title: __('Packagename'), operate: 'LIKE'},
                        {field: 'packagethumbimage', title: __('Packagethumbimage'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'duedate', title: __('Duedate'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
