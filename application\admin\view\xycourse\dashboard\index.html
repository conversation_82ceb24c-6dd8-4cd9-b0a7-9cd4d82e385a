<style>
    .flex{display: flex;}
    .nums{margin: 20px 0 30px;}
    .sales-box{padding: 15px 20px; margin-bottom: 20px;background: #f1f4f6; border-radius: 5px;}
    .sales-box h4{font-size: 18px; line-height: 34px;color: #232529;}
    .sales-box h4 a{float: right;font-size: 14px;font-weight: normal;}
    .sales-box h3{font-size: 30px;font-weight: bold;}
    .sales-box .top{margin-top: 20px;}
    .sales-box p{font-size: 16px;color: #8a8d99;}
    .sales-box .title{font-size: 18px;color: #8a8d99;}
    .sales-box .title strong{margin-left: 3px;}
    .sales-box .btm{margin-top: 30px;}
    .sales-box .btm .item{width: 50%;text-align: center;}
    .sales-box .btm .sx{width: 15%;}
    .sales-box .btm .r{border-left: 1px solid rgb(220, 223, 230)}
    .sales-box h5{font-size: 24px;font-weight: bold;}
    .pln{padding-left: 0;}

    .sm-st-icon {
        width: 60px;
        height: 60px;
        display: inline-block;
        line-height: 60px;
        text-align: center;
        font-size: 30px;
        background: #eee;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        float: left;
        margin-right: 10px;
        color: #fff;
    }

    .sm-st-info {
        padding-top: 2px;
    }

    .sm-st-info span {
        display: block;
        font-size: 24px;
        font-weight: 600;
    }
    
    .orange {
        background: #fa8564 !important;
    }

    .red {
        background: #F05050 !important;
    }

    .green {
        background: #86ba41 !important;
    }

    .pink {
        background: #AC75F0 !important;
    }

    .yellow {
        background: #fdd752 !important;
    }

    .bule {
        background: #23b7e5 !important;
    }
    
</style>

<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null, false)}
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">{:__('Dashboard')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">

                <div class="row nums">
                    <div class="col-sm-2 col-xs-6 pln">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon red"><i class="fa fa-users"></i></span>
                            <div class="sm-st-info">
                                <span>{$total1|htmlentities}</span>
                                会员数
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon orange"><i class="fa fa-th-large"></i></span>
                            <div class="sm-st-info">
                                <span>{$total2|htmlentities}</span>
                                老师数
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon green"><i class="fa fa-id-badge"></i></span>
                            <div class="sm-st-info">
                                <span>{$total3|htmlentities}</span>
                                员工数
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon pink"><i class="fa fa-rmb"></i></span>
                            <div class="sm-st-info">
                                <span>{$total4|htmlentities}</span>
                                总消费
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon yellow"><i class="fa fa-archive"></i></span>
                            <div class="sm-st-info">
                                <span>{$total5|htmlentities}</span>
                                总充值
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon bule"><i class="fa fa-check-circle"></i></span>
                            <div class="sm-st-info">
                                <span>{$total6|htmlentities}</span>
                                完成预约
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">

                    <div class="col-lg-4 col-xs-6">
                        <div class="sales-box">
                            <h4>预约数据 <a class="btn-addtabs" href="{:url('/xycourse/appointment/index?ref=addtabs')}">查看 <i class="fa fa-angle-right"></i></a></h4>
                            <div class="top text-center">
                                <div class="title">累计</div>
                                <div>
                                    <h3>{$at|htmlentities}</h3>
                                    <p>(完成：{$at1|htmlentities})</p>
                                </div>
                            </div>
                            <div class="flex btm">
                                <div class="item">
                                    <div class="title">今日<strong>{$todayAt|htmlentities}</strong></div>
                                    <p>(完成：{$todayAt1|htmlentities})</p>
                                </div>
                                <div class="item r">
                                    <div class="title">本月<strong>{$monthAt|htmlentities}</strong></div>
                                    <p>(完成：{$monthAt1|htmlentities})</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-xs-6">
                        <div class="sales-box">
                            <h4>订单数据 <a class="btn-addtabs" href="{:url('/xycourse/order/order/index?ref=addtabs')}">查看 <i class="fa fa-angle-right"></i></a></h4>
                            <div class="top text-center">
                                <div class="title">累计</div>
                                <div>
                                    <h3>¥{$orderTotalMoney|htmlentities}</h3>
                                    <p>(订单：{$orderTotalNum|htmlentities})</p>
                                </div>
                            </div>
                            <div class="flex btm">
                                <div class="item">
                                    <div class="title">今日<strong>¥{$orderTodayMoney|htmlentities}</strong></div>
                                    <p>(订单：{$orderTodayNum|htmlentities})</p>
                                </div>
                                <div class="item r">
                                    <div class="title">本月<strong>¥{$orderMonthMoney|htmlentities}</strong></div>
                                    <p>(订单：{$orderMonthNum|htmlentities})</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-xs-6">
                        <div class="sales-box">
                            <h4>充值数据<a class="btn-addtabs" href="{:url('/xycourse/recharge/order/index?ref=addtabs')}">查看 <i class="fa fa-angle-right"></i></a></h4>
                            <div class="top text-center">
                                <div class="title">累计</div>
                                <div>
                                    <h3>¥{$rechargeTotalMoney|htmlentities}</h3>
                                    <p>(订单：{$rechargeTotalNum|htmlentities})</p>
                                </div>
                            </div>
                            <div class="flex btm">
                                <div class="item">
                                    <div class="title">今日<strong>¥{$rechargeTodayMoney|htmlentities}</strong></div>
                                    <p>(订单：{$rechargeTodayNum|htmlentities})</p>
                                </div>
                                <div class="item r">
                                    <div class="title">本月<strong>¥{$rechargeMonthMoney|htmlentities}</strong></div>
                                    <p>(订单：{$rechargeMonthNum|htmlentities})</p>
                                </div>
                            </div>
                        </div>
                    </div>
                
                    
                </div>
                
                <div class="row">
                    <section class="col-lg-6 connectedSortable">
                        <div class="sales-box">
                            <h4>订单趋势 (近七日)</h4>
                            <div class="tab-content no-padding">
                                <div class="chart tab-pane active" id="order-chart" style="position: relative; height: 300px;"></div>
                            </div>
                        </div>
                    </section>
                    <section class="col-lg-6 connectedSortable">
                        <div class="sales-box">
                            <h4>销售额 (元)</h4>
                            <div class="tab-content no-padding">
                                <div class="chart tab-pane active" id="recharge-chart" style="position: relative; height: 300px;"></div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
            
        </div>
    </div>
</div>

