<template>
	<view class="course" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		
		<view class="tab flex tc tb p-tb-25 bc-w">
			<view v-for="(item,index) in type" :class="'col-'+type.length" @tap="setTab(index)">
				<view :style="typeIndex == index ? css.tcmc : css.tcm">
					{{item.name}}
					<view class="line" :style="typeIndex == index ? css.mcbg : 'bc-w'"></view>
				</view>
			</view>
		</view>
		
		<block v-if="!isLoading">
			<view class="course-list p-tb-30" v-if="!isEmpty">
				<view class="item br-10 m-lr-30 m-b-30 p-30 bc-w flex" v-for="item in courseList" :key="item.id">
					<view class="l m-r-20">
						<view class="m-t-10 tb">
							{{item.name}}
						</view>
						<view class="m-t-15 flex" :style="css.tcl">
							<view class="ts-28">总次数：{{item.num}}</view>
							<view class="ts-28 m-l-auto">剩余：<text :style="css.tcp">{{item.residue}}次</text></view>
						</view>
						<view class="flex m-t-15 lh-54">
							<view class="ts-28" :style="css.tcl">有效期至：<text :style="css.tcm">{{item.duedate}}</text></view>
						</view>
					</view>
					<view class="r m-l-auto" v-if="item.residue > 0">
						<view class="btn ts-26 tc-w p-lr-40" :style="css.mcbg" @tap="showVerify(item)">核销</view>
					</view>
				</view>
			</view>
			<view v-else>
				<xy-empty :text="'暂无'+(typeIndex>0?type[typeIndex].name:'')+'课程'" />
			</view>
		</block>
		
		<!--核销弹窗-->
		<block v-if="verifyModelShow">
			<view class="xy-modal-box bottom-fixed xy-modal-box-bottom verify-model-box ovh" :style="css.pbg" :class="[verifyModelShow?'xy-modal-show':'']">
				<view class="title p-tb-35 tb tc" :style="css.mbg">{{verifyItem.name}}核销</view>
				
				<view class="p-50 m-lr-40">
					
					<view class="flex m-tb-40 p-b-50 num-box">
						<view @tap="due()" class="numbtn tc-w tc" :style="css.mcbg">-</view>
						<view class="num tc tb">{{verifyNum}}</view>
						<view @tap="add()" class="numbtn tb tc-w tc" :style="css.mcbg">+</view>
					</view>
					
					<view class="flex" :style="css.tcl">
						<view>总次数 <text class="tb" :style="css.tcm">{{verifyItem.num}}</text> 次</view>
						<view class="m-l-auto">剩余 <text class="tb" :style="css.tcmc">{{verifyItem.residue}}</text> 次</view>
					</view>
					<view class="tc-w btn tc" :style="css.mcbg" @tap="verifyComfirm()">确定核销</view>
				</view>
				
				<view class="close ts-40" :style="css.tcl" @tap="verifyModelShow = false"><text class="xyicon icon-close"></text></view>
			</view>
			<view class="xy-modal-mask" :class="[verifyModelShow?'xy-mask-show':'']" @tap="verifyModelShow =!verifyModelShow"></view>
		</block>
		
		
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import xyEmpty from '@/components/xy-empty';
	export default {
		components: {
			xyEmpty,
		},
		data() {
			return {
				css:{},
				isLoading: true,
				isEmpty: true,
				store_id: 0,
				courseList: [],
				currentPage: 1,
				lastPage: 1,
				loadStatus: 'loadmore',
				type:[{name:'全部',value:'all'},{name:'团课',value:'league'},{name:'私教',value:'private'}],
				typeIndex:0,
				verifyModelShow:false,
				verifyItem:{},
				verifyNum:1,
			}
		},
		computed: {
			...mapState(['common'])
		},
		onLoad(options) {
			if(options.status != undefined){
				this.courseStatusIndex = options.status;
			}
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		onPullDownRefresh() {
			this.currentPage = 1;
			this.courseList = [];
			this.loadData();
		},
		onReachBottom() {
			if(this.currentPage < this.lastPage) {
				this.currentPage += 1;
				this.loadData();
			}
		},
		methods: {
			loadData(){
				
				this.$api.post({
					url: '/coach_center/memberCourse',
					loadingTip:'加载中...',
					data: {
						page: this.currentPage,
						user_id: this.$Route.query.user_id,
						type: this.type[this.typeIndex].value,
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.isLoading = false;
						this.courseList = [...this.courseList, ...res.data];
						this.verifyItem = this.courseList[0];
						this.isEmpty = !this.courseList.length;
						this.currentPage = res.current_page; 
						this.lastPage = res.last_page;
						this.loadStatus = this.currentPage < res.last_page ? 'loadmore' : 'nomore';
					}
				});
				
			},
			
			setTab(index){
				this.typeIndex = index;
				this.currentPage = 1;
				this.courseList = [];
				this.loadData();
			},
			
			add(){
				if(this.verifyNum < this.verifyItem.residue){
					this.verifyNum ++ ;
				}
			},
			
			due(){
				if(this.verifyNum > 1){
					this.verifyNum -- ;
				}
			},
			
			//显示核销弹窗
			showVerify(item){
				this.verifyNum = 1;
				this.verifyModelShow = true;
				this.verifyItem = item;
			},
			
			//确认消课
			verifyComfirm(){
				this.$api.post({
					url: '/coach_center/verifyCourse',
					data: {
						user_course_id: this.verifyItem.id,
						num: this.verifyNum,
					},
					success: res => {
						this.$xyfun.msg('核销成功');
						this.verifyModelShow = false;
						
						setTimeout(()=>{
							this.courseList = [];
							this.currentPage = 1;
							this.loadData();
						},2000);
					}
				});
			}
			
		}
	}
</script>

<style scoped lang="scss">
	.tab{
		width: 100%;
		.line{height: 4rpx;width: 60rpx;margin: 10rpx auto 0;}
	}
	
	
	.verify-model-box{
		.num-box{padding-left: 135rpx;}
		.numbtn{width: 100rpx;height: 100rpx;border-radius: 50rpx;font-size: 80rpx;line-height: 90rpx;}
		.num{line-height: 100rpx; width: 100rpx;}
		.btn{height: 70rpx;line-height: 70rpx;border-radius: 35rpx; margin-top: 120rpx;}
		.close{position: absolute; right:30rpx;top: 30rpx;}
	}
	
	.return{line-height: 40rpx;height: 40rpx;}
	
	.course-list{
		.goods{
			line-height: 150rpx;
			.image{width: 150rpx;height: 150rpx;}
			.image image{width: 150rpx;}
		}
	}
	
	.course-list{
		.item{
			.l{
				width: 370rpx;
			}
			.r .btn{
				margin-top: 60rpx;
				height: 60rpx;line-height: 60rpx;border-radius: 30rpx;
			}
		}
	}
	
</style>