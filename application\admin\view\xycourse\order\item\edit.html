<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_id" data-rule="required" data-source="order/index" class="form-control selectpage" name="row[order_id]" type="text" value="{$row.order_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Course_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_id" data-rule="required" data-source="course/index" class="form-control selectpage" name="row[course_id]" type="text" value="{$row.course_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coursename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coursename" data-rule="required" class="form-control" name="row[coursename]" type="text" value="{$row.coursename|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coursethumbimage')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-coursethumbimage" data-rule="required" class="form-control" size="50" name="row[coursethumbimage]" type="text" value="{$row.coursethumbimage|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-coursethumbimage" class="btn btn-danger faupload" data-input-id="c-coursethumbimage" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-coursethumbimage"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-coursethumbimage" class="btn btn-primary fachoose" data-input-id="c-coursethumbimage" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-coursethumbimage"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-coursethumbimage"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Courseprice')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-courseprice" data-rule="required" class="form-control" step="0.01" name="row[courseprice]" type="number" value="{$row.courseprice|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coursetype')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coursetype" data-rule="required" class="form-control" name="row[coursetype]" type="text" value="{$row.coursetype|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-num" data-rule="required" class="form-control" name="row[num]" type="number" value="{$row.num|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
