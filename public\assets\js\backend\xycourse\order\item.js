define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xycourse/order/item/index' + location.search,
                    add_url: 'xycourse/order/item/add',
                    edit_url: 'xycourse/order/item/edit',
                    del_url: 'xycourse/order/item/del',
                    multi_url: 'xycourse/order/item/multi',
                    import_url: 'xycourse/order/item/import',
                    table: 'xycourse_order_item',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'order_id', title: __('Order_id')},
                        {field: 'course_id', title: __('Course_id')},
                        {field: 'coursename', title: __('Coursename'), operate: 'LIKE'},
                        {field: 'coursethumbimage', title: __('Coursethumbimage'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'courseprice', title: __('Courseprice'), operate:'BETWEEN'},
                        {field: 'coursetype', title: __('Coursetype'), operate: 'LIKE'},
                        {field: 'num', title: __('Num')},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
