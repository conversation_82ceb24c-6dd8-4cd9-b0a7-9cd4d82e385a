<?php

return [
    'Id'                         => 'ID',
    'Order_sn'                   => '订单号',
    'User_id'                    => '下单会员',
    'Store_id'                   => '门店ID',
    'Package_id'                 => '课程包ID',
    'Packagename'                => '课程包',
    'Packagethumbimage'          => '课程包缩略图',
    'Storename'                  => '门店名称',
    'Storelogo'                  => '门店Logo',
    'Limitday'                   => '有效期(天)',
    'Total_amount'               => '订单金额',
    'Total_fee'                  => '需付金额',
    'Pay_fee'                    => '实付金额',
    'Transaction_id'             => '交易单号',
    'Payment_json'               => '交易原始数据',
    'Pay_type'                   => '支付方式',
    'Pay_type wechat'            => '微信支付',
    'Pay_type balance'           => '余额支付',
    'Paytime'                    => '支付时间',
    'Platform'                   => '平台',
    'Platform wxminiprogram'     => '微信小程序',
    'Platform wxofficialaccount' => '微信公众号',
    'Ext'                        => '附加字段',
    'Status'                     => '订单状态',
    'Status -2'                  => '已关闭',
    'Set status to -2'           => '设为已关闭',
    'Status -1'                  => '已取消',
    'Set status to -1'           => '设为已取消',
    'Status 0'                   => '待付款',
    'Set status to 0'            => '设为待付款',
    'Status 1'                   => '已付款',
    'Set status to 1'            => '设为已付款',
    'Createtime'                 => '创建时间',
    'Updatetime'                 => '更新时间'
];
