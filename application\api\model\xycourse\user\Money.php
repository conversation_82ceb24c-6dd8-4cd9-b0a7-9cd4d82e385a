<?php

namespace app\api\model\xycourse\user;

use think\Model;
use app\api\model\xycourse\user\User;

class Money extends Model
{

    // 表名
    protected $name = 'xycourse_user_money';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'type_text'
    ];

    
    public function getTypeList()
    {
        return [
                'sys' => __('后台操作'),
                'recharge' =>  __('充值'),    
                'pay_order' => __('订单支付'),
                'return_order' => __('订单退款'),  
            ];
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    

    /**
     * 列表
     */
    public static function getLists($params)
    {
        extract($params);
        $user = User::info();
        $list = self::where(['user_id'=>$user->id])->order('id desc')->paginate();
        return $list;
    }
    







}
