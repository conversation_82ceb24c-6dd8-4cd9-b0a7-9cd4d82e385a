define(['jquery', 'bootstrap', 'backend', 'table', 'form','xycourse_vue'], function ($, undefined, Backend, Table, Form,Vue,Vuedraggable) {

    var Controller = {
        index: function () {
		    
			var vm = new Vue({
				el: '#bottomNav',
				data() {
					return {
						tabbarList:{
							backgroundColor: "#FFFFFF",
							textColor: "#333333",
							textHoverColor: "#fc6d21",
							list:[
								{
									title: "首页",
									link: "/pages/index",
									iconPath: "/assets/addons/xycourse/imgs/tabbar/index.png",
									selectedIconPath: "/assets/addons/xycourse/imgs/tabbar/index-a.png",
									show: 1,
								},
								{
									title: "约课",
									link: "/pages/appointment",
									iconPath: "/assets/addons/xycourse/imgs/tabbar/appointment.png",
									selectedIconPath: "/assets/addons/xycourse/imgs/tabbar/appointment-a.png",
									show: 1,
								},
								{
									title: "购课",
									link: "/pages/package",
									iconPath: "/assets/addons/xycourse/imgs/tabbar/course.png",
									selectedIconPath: "/assets/addons/xycourse/imgs/tabbar/course-a.png",
									show: 1,
								},
								{
									title: "我的",
									link: "/pages/user",
									iconPath: "/assets/addons/xycourse/imgs/tabbar/user.png",
									selectedIconPath: "/assets/addons/xycourse/imgs/tabbar/user-a.png",
									show: 1,
								},
							],
						},
					}
				},
				created() {
					this.operationData();
                },
			
				methods: {
					operationData() {
                        if (Config.row) {
                            for (key in this.tabbarList) {
                                if (Config.row[key]) {
                                    if (Config.row[key] instanceof Object) {
                                        for (inner in Config.row[key]) {
                                            if (Config.row[key][inner]) {
                                                this.tabbarList[key][inner] = Config.row[key][inner]
                                            }
                                        }
                                    } else {
                                        this.tabbarList[key] = Config.row[key]
                                    }
                                }
                            }
                        }
                    },

					submitFrom() {
                        var submitData = JSON.parse(JSON.stringify(this.tabbarList))
						Fast.api.ajax({
							url: 'xycourse/tabbar/index',
							loading: true,
							type: 'POST',
							data: {
								data: JSON.stringify(submitData),
							},
						}, function (ret, res) {
							
						})
                    },

					// 上传图片
					uploadImage(event, key, type) {
						var files = event.target.files[0]; //获取input的图片file值
						var formData = new FormData();
						var upload = Config.upload;
						if(upload.storage !== 'local'){
							var multipart = Object.entries(upload.multipart)[0];
							formData.append(multipart[0], multipart[1]);
						}
						formData.append('file', files, files.name);
						var item = this.tabbarList['list'][key],that = this;
						Fast.api.ajax({
						    url: upload.uploadurl, 
							data:formData,
							processData:false,
							contentType:false,
						}, function(data, ret){
							if(type == 1){
								item['iconPath'] = data.fullurl;
							}else{
								item['selectedIconPath'] = data.fullurl;
							}
							
							Vue.set(that.tabbarList['list'],key, item)
						});
					},
					
					// 选择链接
					selectLink(key){
						var item = this.tabbarList['list'][key],that = this;
						parent.Fast.api.open("xycourse/link/select?multiple=false", __('选择链接'), {
							area: ['70%', '80%'],
						    callback: function (data) {
								item['link'] = data.url;
								Vue.set(that.tabbarList['list'],key, item)
						    }
						});
					},

					tnameInput(event,key){
						var item = this.tabbarList['list'][key],that = this;
						Vue.set(that.tabbarList['list'],key, item)
					}
					
				}
			});
			
			
		},
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
