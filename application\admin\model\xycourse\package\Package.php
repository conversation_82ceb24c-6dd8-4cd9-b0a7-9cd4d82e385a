<?php

namespace app\admin\model\xycourse\package;

use think\Model;

class Package extends Model
{

    

    

    // 表名
    protected $name = 'xycourse_package';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [];

    public function getStatusList()
    {
        return ['normal' => __('Status normal'), 'hidden' => __('Status hidden')];
    }


    public function getDisRuleList()
    {
        return ['0' => __('默认规则'),'1' => __('单独设置')];

    }
    
    public function getDisRuleTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_sku']) ? $data['is_sku'] : '');
        $list = $this->getDisRuleList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getIsDisList()
    {
        return ['1' => __('参与'),'0' => __('不参与')];
    }
    
    public function getIsDisTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_sku']) ? $data['is_sku'] : '');
        $list = $this->getIsDisList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    



}
