define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xycourse/appointment/index' + location.search,
                    //add_url: 'xycourse/appointment/add',
                    //edit_url: 'xycourse/appointment/edit',
                    //del_url: 'xycourse/appointment/del',
                    multi_url: 'xycourse/appointment/multi',
                    import_url: 'xycourse/appointment/import',
                    table: 'xycourse_appointment',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'type', title: __('Type'), searchList: {"league":__('Type league'),"private":__('Type private')}, formatter: Table.api.formatter.normal},
                        {field: 'user.nickname',operate: 'LIKE', title: __('User_id'),formatter:function(v,row){
                                if(v == null){
                                    return '-';
                                }else{
                                    var html = '<div style="display:flex"><img width="50px" height="50px" src="'+row.user.avatar+'" /><p style="text-align:left;line-height:20px;margin-top:5px;margin-left:10px">'+row.user.nickname+'(ID:'+row.user.id+')<br/>'+row.user.mobile+'</p></div>';
                                    return html;
                                }
                            }
                        },
                        {field: 'coursename', title: __('Coursename'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'coachrealname', title: __('Coachrealname'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'date', title: __('Date'), operate: 'LIKE'},
                        {field: 'timestr', title: __('Timestr'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"-1":__('Status -1'),"1":__('Status 1'),"2":__('Status 2')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,
                            buttons :[
                                {
                                    name: 'detail',
                                    dropdown: '操作',
                                    text: __('详情'),
                                    title:  __('预约详情'),
                                    classname: 'btn btn-xs btn-dialog',
                                    extend: 'data-area=\'["80%", "80%"]\'',
                                    url: function (row) {
                                        return `xycourse/appointment/detail?id=${row.id}`;
                                    }
                                },
                                {
                                    name: 'verify',
                                    dropdown: '操作',
                                    text: __('完成'),
                                    title: __('完成'),
                                    classname: 'btn btn-xs btn-ajax',
                                    extend: 'data-area=\'["70%", "80%"]\'',
                                    url: function (row) {
                                        return `xycourse/appointment/verify?ids=${row.id}`;
                                    },
                                    visible: function (row) {
                                        return row.status == 1;
                                    },
                                    confirm:'确定课程已上完吗？',
                                    refresh: true,
                                },
                                {
                                    name: 'cancel',
                                    dropdown: '操作',
                                    text: __('取消'),
                                    title: __('取消'),
                                    classname: 'btn btn-xs btn-ajax',
                                    extend: 'data-area=\'["70%", "80%"]\'',
                                    url: function (row) {
                                        return `xycourse/appointment/cancel?ids=${row.id}`;
                                    },
                                    visible: function (row) {
                                        return row.status == 1;
                                    },
                                    confirm:'确定要取消预约吗？',
                                    refresh: true,
                                },
                            ],
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
