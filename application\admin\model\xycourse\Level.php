<?php

namespace app\admin\model\xycourse;

use think\Model;


class Level extends Model
{

    

    

    // 表名
    protected $name = 'xycourse_level';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'grade_text',
        'upgradetype_text',
        'status_text'
    ];
    
    //条件列表
    public function getConditionList()
    {
        return ['c1' => __('累计充值金额'),'c2' => __('累计消费金额')];
    }
    
    public function getGradeList()
    {
        return ['1' => __('Grade 1'), '2' => __('Grade 2'), '3' => __('Grade 3'), '4' => __('Grade 4'), '5' => __('Grade 5'), '6' => __('Grade 6'), '7' => __('Grade 7'), '8' => __('Grade 8'), '9' => __('Grade 9'), '10' => __('Grade 10')];
    }

    public function getUpgradetypeList()
    {
        return ['or' => __('Upgradetype or')];
    }

    public function getStatusList()
    {
        return ['normal' => __('Status normal'), 'hidden' => __('Status hidden')];
    }


    public function getGradeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['grade']) ? $data['grade'] : '');
        $list = $this->getGradeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getUpgradetypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['upgradetype']) ? $data['upgradetype'] : '');
        $list = $this->getUpgradetypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

}
