
<div class="row">
	<div class="col-md-12" style="padding-bottom: 15px;">
		<span>上课时间：{$row.date|htmlentities} - {$row.timestr|htmlentities}</span>
		<span style="margin-left: 40px;">上课学员：{$row.user.nickname|htmlentities}(ID:{$row.user.id|htmlentities}) {$row.user.mobile|htmlentities}</span>
	</div>
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">课程信息</div>
			<div class="panel-body">
				<table class="table table-bordered">
					<thead>
						<tr>
							<th class="text-center">
								<div class="th-inner">课程ID</div>
							</th>
							<th class="text-center">
								<div class="th-inner">课程主图</div>
							</th>
							<th class="text-center">
								<div class="th-inner">课程名称</div>
							</th>
							
						</tr>
					</thead>
					<tbody>
						<tr>
							<td class="text-center">{$row.course.id|htmlentities}</td>
							<td class="text-center"><img class="img-sm img-center" src="{$row.course.thumbimage|cdnurl|htmlentities}"></td>
							<td class="text-center"><strong>{$row.course.name|htmlentities}</strong></td>
						</tr>
					</tbody>
					<tfoot>
						<tr>
							<th colspan="8" style="text-align: right;">
                				<span style="margin-right:15px;">上课老师：{$row.coach.realname|htmlentities}</span>
							</th>
						</tr>
					</tfoot>
				</table>
				<div style="color: #333;">
					<p style="margin-bottom: 15px;">状态：
						<span class="label label-info">{$row.status_text|htmlentities}</span>
					</p>
				</div>
			</div>
		</div>
	</div>
	
	
	

	
</div>

<div class="hide layer-footer">
	<label class="control-label col-xs-12 col-sm-2"></label>
	<div class="col-xs-12 col-sm-8">
		<button type="reset" class="btn btn-primary btn-embossed btn-close" onclick="Layer.closeAll();">{:__('Close')}</button>
	</div>
</div>
