<template>
	<view class="store-info" :style="{'backgroundColor':data.params.bgColor,'border-radius': data.params.borderRadius+'rpx','margin':'0 '+ data.params.lrmargin+'rpx',padding:data.params.njj+'rpx'}">
		<view class="br-10">
			<view class="top flex" @tap="$xyfun.to('/pages/store/detail')">
				<view class="logo m-r-20">
					<image :src="$xyfun.image(data.params.logo)" class="br-10" />
				</view>
				<view>
					<view class="ts-32 tb">{{data.params.name}}</view>
				</view>
			</view>
			<view class="address ovh m-t-30">
				<text class="xyicon icon-loc m-r-10"></text> {{data.params.address}}
			</view>
			<view class="time flex m-t-30">
				<view class="h ts-32">营业时间：{{data.params.businesshours}}</view>
				<view class="m-l-auto flex" :style="css.tcl">
					<view class="weixin tc m-r-30" @tap="$xyfun.phone(data.params.phone)">
						<view class="c" :style="css.mcbg"><text class="xyicon icon-phone tc-w"></text></view>
						<view class="ts-24 h-24 m-t-5">咨询</view>
					</view>
					<view class="weixin tc m-r-30" @tap="$xyfun.copy(data.params.weixin,'已复制微信号，去添加好友吧')">
						<view class="c" :style="css.mcbg"><text class="xyicon icon-weixin tc-w"></text></view>
						<view class="ts-24 h-24 m-t-5">微信</view>
					</view>
					<view class="weixin tc"  @tap="$xyfun.openLoc(data.params.latitude,data.params.longitude,data.params.name,data.params.address)">
						<view class="c" :style="css.mcbg"><text class="xyicon icon-dh tc-w"></text></view>
						<view class="ts-24 h-24 m-t-5">导航</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		name: "xyStore",
		props: {
			data: {
				type: Object,
			},
		},
		data() {
			return {
				css:this.$xyfun.css(),
			};
		},
		methods: {
			
		}
	}
</script>
<style lang="scss">
	.store-info{
		.top{line-height: 80rpx;}
		.logo,.logo image{width: 80rpx;height: 80rpx;line-height: 80rpx;}
		.time{
			.h{line-height: 79rpx;}
			.c{
				width: 50rpx;
				height: 50rpx;
				line-height: 50rpx;
				border-radius: 25rpx;
			}
		}
	}
</style>
