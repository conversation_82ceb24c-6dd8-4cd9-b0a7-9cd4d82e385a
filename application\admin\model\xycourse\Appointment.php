<?php

namespace app\admin\model\xycourse;

use think\Model;


class Appointment extends Model
{
    

    // 表名
    protected $name = 'xycourse_appointment';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_text',
        'status_text',
        'ext_arr'
    ];
    
    public function getExtArrAttr($value, $data)
    {
        $ext = (isset($data['ext']) && $data['ext']) ? json_decode($data['ext'], true) : [];
        return $ext;
    }

    
    public function getTypeList()
    {
        return ['league' => __('Type league'), 'private' => __('Type private')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2'),'-1' => __('Status -1')];
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function setExt($appointment, $field, $origin = [])
    {
        $newExt = array_merge($origin, $field);

        $orderExt = $appointment['ext_arr'];

        return array_merge($orderExt, $newExt);
    }

    public function coach()
    {
        return $this->belongsTo('\app\admin\model\xycourse\coach\Coach', 'coach_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function course()
    {
        return $this->belongsTo('\app\admin\model\xycourse\Course', 'course_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function user()
    {
        return $this->belongsTo('\app\common\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


}
