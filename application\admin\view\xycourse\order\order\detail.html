
<div class="row">

	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">订单详情</div>
			<div class="panel-body">
				<div class="col-sm-4">
					<p><span>订单号：</span><strong>{$row.order_sn|htmlentities}</strong> </p>
					<p><span>下单时间：</span><strong>{$row.createtime|datetime|htmlentities}</strong> </p>
					{if($row.status > 0)}
					<p><span>付款方式：</span><strong>{$row.pay_type_text|htmlentities}</strong> </p>
						{if($row.pay_type == 'wechat')}
						<p><span>交易流水号：</span><strong>{$row.transaction_id|htmlentities}</strong> </p>
						{/if}
					{/if}
				</div>

				<div class="col-sm-4" style="border-left: solid 1px #e6e6e6;">
					<p><span>会员ID：</span><strong>{$row.user.id|htmlentities}</strong> </p>
					<p><span>会员昵称：</span><strong>{$row.user.nickname|htmlentities}</strong> </p>
					<p><span>联系电话：</span><strong>{$row.user.mobile|htmlentities}</strong> </p>
				</div>
			</div>
		</div>
	</div>
	
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">订单状态</div>
			<div class="panel-body">
				<div class="col-sm-6">
					<div>
						<span>订单状态：</span><strong>{$row.status_text|htmlentities}</strong>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">定场信息</div>
			<div class="panel-body">
				<table class="table table-bordered">
					<thead>
						<tr>
							<th class="text-center">
								<div class="th-inner">门店</div>
							</th>
							<th class="text-center">
								<div class="th-inner">场地</div>
							</th>
							<th class="text-center">
								<div class="th-inner">区域</div>
							</th>
							<th class="text-center">
								<div class="th-inner">时间</div>
							</th>
							<th class="text-center">
								<div class="th-inner">价格</div>
							</th>
						</tr>
					</thead>
					<tbody>
						{volist name="row.item" id="vo"}
						<tr>
							<td class="text-center">{$row.storename|htmlentities}</td>
							<td class="text-center">{$row.venuename|htmlentities}</td>
							<td class="text-center">{$vo.area|htmlentities}</td>
							<td class="text-center">{$vo.date|htmlentities} {$vo.week|htmlentities} {$vo.starttime|htmlentities}-{$vo.endtime|htmlentities} </td>
							<td class="text-center">{$vo.price|htmlentities}</td>
						</tr>
						{/volist}
					</tbody>
					<tfoot>
						<tr>
							<th colspan="10" style="text-align: right;">
								<span class="ordertext" style="margin-right:100px;">订单总价：<samp class="text-red">￥{$row.total_amount|htmlentities}</samp></span>
								<span class="ordertext" style="margin-right:100px;">应付金额：<samp class="text-red">￥{$row.total_fee|htmlentities}</samp></span>
								<span class="ordertext" style="margin-right: 15px;">实付金额：<samp class="text-red">￥{$row.pay_fee|htmlentities}</samp></span>
							</th>
						</tr>
					</tfoot>
				</table>
			</div>
		</div>
	</div>
</div>

<div class="hide layer-footer">
	<label class="control-label col-xs-12 col-sm-2"></label>
	<div class="col-xs-12 col-sm-8">
		<button type="reset" class="btn btn-primary btn-embossed btn-close" onclick="Layer.closeAll();">{:__('Close')}</button>
	</div>
</div>
