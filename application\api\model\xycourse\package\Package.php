<?php

namespace app\api\model\xycourse\package;
use app\api\model\xycourse\package\Item as ItemModel;
use app\api\model\xycourse\store\Store as StoreModel;

use think\Model;

class Package extends Model
{

    // 表名
    protected $name = 'xycourse_package';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [];
    
    /**
     * 列表
     */
    public static function getLists($params)
    {
        extract($params);
        $where = [
            'status'   => 'normal',
        ];
        $order = 'id asc';
        $data = self::where($where)->field('*,(sales + virtualsales) as total_sales')->order($order);
        if (isset($page)) {
            $data = $data->paginate();
        } else {
            if(isset($limit)){
                $data = $data->limit($limit)->select();
            }else{
                $data = $data->select();
            }
        }
        return $data;
    }

    public static function getDetail($params)
    {
        extract($params);
        $detail = (new self)->where('id', $id)->field('*,(sales + virtualsales) as total_sales')->find();

        if (!$detail || $detail->status == 'hidden') {
            return null;
        }

        $courseList = ItemModel::with(['course'])->where(['package_id'=>$detail['id']])->select();
        $detail['courselist'] = $courseList;

        $storeInfo = StoreModel::where(['id'=>1])->find();

        $detail['storeInfo'] = $storeInfo;

        return $detail;
    }

    public function getImagesAttr($value, $data)
    {
        $imagesArray = [];
        if (!empty($value)) {
            $imagesArray = explode(',', $value);
            foreach ($imagesArray as &$v) {
                $v = cdnurl($v, true);
            }
            return $imagesArray;
        }
        return $imagesArray;
    }

    public function getThumbimageAttr($value, $data)
    {
        if (!empty($value)) return cdnurl($value, true);
    }

    public function item()
    {
        return $this->hasMany(ItemModel::class, 'package_id', 'id');
    }

}
