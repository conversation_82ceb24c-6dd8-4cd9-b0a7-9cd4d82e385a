<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-package {:$auth->check('xycourse/user/course/package')?'':'hide'}" title="{:__('购买课程包')}" ><i class="fa fa-plus"></i> {:__('购买课程包')}</a>
                        
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('xycourse/user/course/add')?'':'hide'}" title="{:__('添加课程')}" ><i class="fa fa-plus"></i> {:__('添加课程')}</a>
                        

                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('xycourse/user/course/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                    
                        <!--a href="javascript:;" class="btn btn-info btn-import" ><i class="fa fa-plus"></i> {:__('导入')}</a>
                        <a href="/assets/addons/xycourse/file/会员课程导入模板.xlsx" target="_blank">{:__('下载导入模板')}</a-->
                        
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('xycourse/user/course/edit')}"
                           data-operate-del="{:$auth->check('xycourse/user/course/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
