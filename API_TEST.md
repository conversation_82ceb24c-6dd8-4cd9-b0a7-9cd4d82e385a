# 用户注册登录接口测试文档

## 接口基础信息
- 基础URL: `http://39.106.22.43:818/api/xycourse`
- 请求方式: POST
- 请求头: `Content-Type: application/x-www-form-urlencoded`

## 1. 获取验证码接口

**接口地址:** `/user/captcha`

**请求参数:**
```
mobile: 手机号 (必填，格式：1[3-9]xxxxxxxxx)
```

**请求示例:**
```bash
curl -X POST "http://39.106.22.43:818/api/xycourse/user/captcha" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "mobile=13800138000"
```

**响应示例:**
```json
{
  "code": 1,
  "msg": "验证码已发送",
  "time": "1640995200",
  "data": null
}
```

## 2. 验证码注册接口

**接口地址:** `/user/register`

**请求参数:**
```
mobile: 手机号 (必填)
captcha: 验证码 (必填，6位数字)
```

**请求示例:**
```bash
curl -X POST "http://39.106.22.43:818/api/xycourse/user/register" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "mobile=13800138000&captcha=123456"
```

## 3. 验证码登录接口

**接口地址:** `/user/login`

**请求参数:**
```
mobile: 手机号 (必填)
captcha: 验证码 (必填，6位数字)
```

**请求示例:**
```bash
curl -X POST "http://39.106.22.43:818/api/xycourse/user/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "mobile=13800138000&captcha=123456"
```

## 4. 密码注册接口

**接口地址:** `/user/passwordRegister`

**请求参数:**
```
username: 用户名 (必填)
password: 密码 (必填，最少6位)
mobile: 手机号 (可选)
```

**请求示例:**
```bash
curl -X POST "http://39.106.22.43:818/api/xycourse/user/passwordRegister" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=123456&mobile=13800138000"
```

## 5. 密码登录接口

**接口地址:** `/user/passwordLogin`

**请求参数:**
```
username: 用户名 (必填)
password: 密码 (必填)
```

**请求示例:**
```bash
curl -X POST "http://39.106.22.43:818/api/xycourse/user/passwordLogin" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=123456"
```

## 6. 用户信息完善接口

**接口地址:** `/user/profile`

**请求参数:**
```
nickname: 昵称 (必填)
avatar: 头像URL (可选)
mobile: 手机号 (可选)
```

**请求头:**
```
token: 用户登录后获得的token
```

**请求示例:**
```bash
curl -X POST "http://39.106.22.43:818/api/xycourse/user/profile" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "token: YOUR_TOKEN_HERE" \
  -d "nickname=测试用户&avatar=http://example.com/avatar.jpg"
```

## 7. 刷新用户信息接口

**接口地址:** `/user/refresh`

**请求头:**
```
token: 用户登录后获得的token
```

**请求示例:**
```bash
curl -X POST "http://39.106.22.43:818/api/xycourse/user/refresh" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "token: YOUR_TOKEN_HERE"
```

## 8. 退出登录接口

**接口地址:** `/user/logout`

**请求头:**
```
token: 用户登录后获得的token
```

**请求示例:**
```bash
curl -X POST "http://39.106.22.43:818/api/xycourse/user/logout" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "token: YOUR_TOKEN_HERE"
```

## 响应格式说明

**成功响应:**
```json
{
  "code": 1,
  "msg": "操作成功",
  "time": "1640995200",
  "data": {
    "userInfo": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户",
      "mobile": "13800138000",
      "avatar": "http://example.com/avatar.jpg",
      "token": "abcdef123456...",
      "is_info": 1
    }
  }
}
```

**错误响应:**
```json
{
  "code": 0,
  "msg": "错误信息",
  "time": "1640995200",
  "data": null
}
```

## 注意事项

1. 验证码在开发环境下会通过 `trace()` 函数记录到日志中，生产环境需要接入短信服务
2. 所有需要登录的接口都需要在请求头中携带 `token`
3. `is_info` 字段表示用户是否需要完善信息：1-需要完善，2-不需要完善
4. 密码长度最少6位
5. 手机号格式：1[3-9]xxxxxxxxx

## 测试流程

1. 调用获取验证码接口
2. 查看服务器日志获取验证码（开发环境）
3. 使用验证码进行注册或登录
4. 获取返回的token
5. 使用token调用其他需要登录的接口
