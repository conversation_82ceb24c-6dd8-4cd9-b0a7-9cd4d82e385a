<template>
	<view class="order" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		<view class="tab flex tc tb p-tb-25 bc-w">
			<view v-for="(item,index) in orderStatus" :class="'col-'+orderStatus.length" @tap="setTab(index)">
				<view :style="orderStatusIndex == index ? css.tcmc : css.tcm">
					{{item.name}}
					<view class="line" :style="orderStatusIndex == index ? css.mcbg : 'bc-w'"></view>
				</view>
			</view>
		</view>
		<block v-if="!isLoading">
			<view class="order-list p-tb-30" v-if="!isEmpty">
				<view class="item br-10 m-lr-30 m-b-30 p-30 p-t bc-w" v-for="item in orderList" :key="item.id" @tap="$xyfun.to('/pages/user/order/detail?id='+item.id+'&type=order')">
					<view class="flex">
						<view>订单号：{{item.order_sn}}</view>
						<view class="m-l-auto tb" :style="css.tcmc">{{item.status_text}}</view>
					</view>
					
					<view class="item-list flex m-t-30">
						<view class="item flex p-tb-30" :key="index" @tap="$xyfun.to('/pages/package/detail?id='+item.package_id)">
							<view class="l"><image :src="item.packagethumbimage" class="br-10" /></view>
							<view class="r m-l-20">
								<view class="m-t-10 tb">
									{{item.packagename}}
								</view>
								<view class="ts-36 tb m-t-15" :style="css.tcp">
									<text class="ts-28">¥</text>{{item.total_amount}}
								</view>
								<view class="flex m-t-15 lh-54">
									<view class="ts-28" :style="css.tcl">有效期：<text :style="css.tcm">{{item.limitday}}</text>天</view>
								</view>
							</view>
						</view>
					</view>
					
					<view :style="css.pbg" class="flex p-20 m-t-30">
						<view>合计：¥{{item.total_amount}}</view>
						<view class="m-l-auto lh-30 flex" :style="css.tcmc">订单详情 <text class="xyicon icon-right"></text></view>
					</view>
				</view>
			</view>
			<view v-else>
				<xy-empty :text="'暂无'+(orderStatusIndex>0?orderStatus[orderStatusIndex].name:'')+'订单'" />
			</view>
		</block>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import xyEmpty from '@/components/xy-empty';
	export default {
		components: {
			xyEmpty,
		},
		data() {
			return {
				css:{},
				isLoading:true,
				isEmpty: true,
				orderList: [],
				currentPage: 1,
				lastPage: 1,
				loadStatus: 'loadmore',
				orderStatus:[
					{value:'all',name:'全部'},
					{value:'0',name:'待付款'},
					{value:'1',name:'已付款'},
					{value:'-1',name:'已取消'},
					{value:'-2',name:'已关闭'},
				],
				orderStatusIndex:0,
			}
		},
		computed: {
			...mapState(['common'])
		},
		onLoad(options) {
			if(options.status != undefined){
				this.orderStatusIndex = options.status;
			}
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		onPullDownRefresh() {
			this.currentPage = 1;
			this.orderList = [];
			this.loadData();
		},
		onReachBottom() {
			if(this.currentPage < this.lastPage) {
				this.currentPage += 1;
				this.loadData();
			}
		},
		methods: {
			loadData(){
				this.$api.post({
					url: '/order/lists',
					loadingTip:'加载中...',
					data: {
						page: this.currentPage,
						status: this.orderStatus[this.orderStatusIndex]['value']
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.isLoading = false;
						this.orderList = [...this.orderList, ...res.data];
						this.isEmpty = !this.orderList.length;
						this.currentPage = res.current_page; 
						this.lastPage = res.last_page;
						this.loadStatus = this.currentPage < res.last_page ? 'loadmore' : 'nomore';
					}
				});
			},
			
			setTab(index){
				this.orderStatusIndex = index;
				this.currentPage = 1;
				this.orderList = [];
				this.loadData();
			},
			
		}
	}
</script>

<style scoped lang="scss">
	.tab{
		width: 100%;
		.line{height: 4rpx;width: 60rpx;margin: 10rpx auto 0;}
	}
	
	.return{line-height: 40rpx;height: 40rpx;}
	
	.order-list{
		.goods{
			line-height: 150rpx;
			.image{width: 150rpx;height: 150rpx;}
			.image image{width: 150rpx;}
		}
	}
	
	.item-list{
		.item{
			.l, .l image{
				width: 240rpx;height: 180rpx;
			}
			.r{
				width: 370rpx;
				.price-box{position: absolute; right: 0;top: 0;}
			}
		}
	}
	
</style>