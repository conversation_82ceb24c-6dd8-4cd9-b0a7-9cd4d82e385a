<?php

namespace addons\xycourse\service;

use app\api\model\xycourse\order\Order as OrderModel;
use think\queue\Job;
use think\Log;
use think\Db;


/**
 * 订单自动操作
 */
class QueueOrder
{

    /**
     * 订单自动关闭
     */
    public static function autoClose(Job $job, $params){

        try {
            $orderData = $params['order'];
            $orderType = $params['type']; // 订单类型

            // 重新查询订单
            $order = null;

            // 课程包订单
            if ($orderType == 'order') {
                $order = OrderModel::where(['id'=>$orderData['id']])->find();
            }

            if ($order && $order['status'] == 0) {
                Db::transaction(function () use ($order) {
                    $order->status = -2;
                    $order->ext = json_encode($order->setExt($order, ['invalid_time' => time()]));// 自动取消时间
                    $order->save();
                });
            }

            $job->delete();
        } catch (\Exception $e) {
            Log::write('订单自动关闭队列执行失败，错误信息：' . $e->getMessage());
        }
    }
    
}