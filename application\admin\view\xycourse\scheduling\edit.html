<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coach_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coach_id" data-rule="required" data-source="coach/index" class="form-control selectpage" name="row[coach_id]" type="text" value="{$row.coach_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Course_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_id" data-rule="required" data-source="course/index" class="form-control selectpage" name="row[course_id]" type="text" value="{$row.course_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-type" class="form-control selectpicker" name="row[type]">
                {foreach name="typeList" item="vo"}
                    <option value="{$key|htmlentities}" {in name="key" value="$row.type"}selected{/in}>{$vo|htmlentities}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[date]" type="text" value="{$row.date|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Starttime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-starttime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[starttime]" type="text" value="{:$row.starttime?datetime($row.starttime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Endtime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-endtime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[endtime]" type="text" value="{:$row.endtime?datetime($row.endtime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Timestr')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-timestr" data-rule="required" class="form-control" name="row[timestr]" type="text" value="{$row.timestr|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('People')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-people" data-rule="required" class="form-control" name="row[people]" type="number" value="{$row.people|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
