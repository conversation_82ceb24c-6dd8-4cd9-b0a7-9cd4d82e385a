<?php

namespace app\admin\controller\xycourse;

use app\common\controller\Backend;
use app\admin\model\xycourse\order\Order;
use app\admin\model\User;
use app\admin\model\xycourse\coach\Coach;
use app\admin\model\xycourse\Staff;
use app\admin\model\xycourse\Appointment;
use app\admin\model\xycourse\recharge\Order as RechargeOrder;

/**
 * 控制台
 */
class Dashboard extends Backend
{

    /**
     * 查看
     */
    public function index()
    {

        $this->view->assign([
            'total1'         => User::count(), //总会员数
            'total2'        => Coach::count(), //总老师数
            'total3'        => Staff::count(), //总员工数
            'total4'      => User::sum('xycourse_consume'), //总消费
            'total5'      => User::sum('xycourse_recharge'), //总充值
            'total6'      => Appointment::where(['status'=>2])->count(), //完成预约
        ]);

        //今日起始时间戳
        $beginToday = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
        $endToday = mktime(0, 0, 0, date("m"), date("d") + 1, date("Y")) - 1;
        //本月起始时间戳
        $beginThismonth = mktime(0, 0, 0, date("m"), 1, date("Y"));
        $endThismonth = mktime(23, 59, 59, date("m"), date("t"), date("Y"));


        //总预约
        $at = Appointment::count();
        //总完成预约
        $at1 = Appointment::where(['status'=>2])->count();
        //今日预约
        $todayAt = Appointment::where(['createtime' => ['between', [$beginToday, $endToday]]])->count();
        //今日完成预约
        $todayAt1 = Appointment::where(['status'=>2,'createtime' => ['between', [$beginToday, $endToday]]])->count();
        //本月预约
        $monthAt = Appointment::where(['createtime' => ['between', [$beginThismonth, $endThismonth]]])->count();
        //本月完成预约
        $monthAt1 = Appointment::where(['status'=>2,'createtime' => ['between', [$beginThismonth, $endThismonth]]])->count();

        $this->view->assign("at", $at);
        $this->view->assign("at1", $at1);
        $this->view->assign("todayAt", $todayAt);
        $this->view->assign("todayAt1", $todayAt1);
        $this->view->assign("monthAt", $monthAt);
        $this->view->assign("monthAt1", $monthAt1);

        //定场总销售额
        $orderTotalMoney = Order::where(['status'=>1])->sum('total_fee');
        //定场总订单数
        $orderTotalNum = Order::where(['status'=>1])->count();
        //定场今日销售额
        $orderTodayMoney = Order::where(['status'=>1,'createtime' => ['between', [$beginToday, $endToday]]])->sum('total_fee');
        //定场今日订单数
        $orderTodayNum = Order::where(['status'=>1,'createtime' => ['between', [$beginToday, $endToday]]])->count();
        //定场本月销售额
        $orderMonthMoney = Order::where(['status'=>1,'createtime' => ['between', [$beginThismonth, $endThismonth]]])->sum('total_fee');
        //定场本月订单数
        $orderMonthNum = Order::where(['status'=>1,'createtime' => ['between', [$beginThismonth, $endThismonth]]])->count();

        //充值总销售额
        $rechargeTotalMoney = RechargeOrder::where(['status'=>1])->sum('total_fee');
        //充值总订单数
        $rechargeTotalNum = RechargeOrder::where(['status'=>1])->count();
        //充值今日销售额
        $rechargeTodayMoney = RechargeOrder::where(['status'=>1,'createtime' => ['between', [$beginToday, $endToday]]])->sum('total_fee');
        //充值今日订单数
        $rechargeTodayNum = RechargeOrder::where(['status'=>1,'createtime' => ['between', [$beginToday, $endToday]]])->count();
        //充值本月销售额
        $rechargeMonthMoney = RechargeOrder::where(['status'=>1,'createtime' => ['between', [$beginThismonth, $endThismonth]]])->sum('total_fee');
        //充值本月订单数
        $rechargeMonthNum = RechargeOrder::where(['status'=>1,'createtime' => ['between', [$beginThismonth, $endThismonth]]])->count();


        $this->view->assign("orderTotalMoney", $orderTotalMoney);
        $this->view->assign("orderTotalNum", $orderTotalNum);
        $this->view->assign("orderTodayMoney", $orderTodayMoney);
        $this->view->assign("orderTodayNum", $orderTodayNum);
        $this->view->assign("orderMonthMoney", $orderMonthMoney);
        $this->view->assign("orderMonthNum", $orderMonthNum);

        $this->view->assign("rechargeTotalMoney", $rechargeTotalMoney);
        $this->view->assign("rechargeTotalNum", $rechargeTotalNum);
        $this->view->assign("rechargeTodayMoney", $rechargeTodayMoney);
        $this->view->assign("rechargeTodayNum", $rechargeTodayNum);
        $this->view->assign("rechargeMonthMoney", $rechargeMonthMoney);
        $this->view->assign("rechargeMonthNum", $rechargeMonthNum);

        $weekDays = xycourseGetWeeks();

        $orderDayTotalMoney = [];
        $orderDayTotalNum = [];
        $rechargeDayTotalMoney = [];
        $rechargeDayTotalNum = [];
        foreach($weekDays as $wd){
            $orderDayTotalMoney[] = Order::where(['createtime'=>['between', [strtotime(date('Y').'-'.$wd), strtotime(date('Y').'-'.$wd)+86400]]])->sum('total_fee');
            $orderDayTotalNum[] = Order::where(['createtime'=>['between', [strtotime(date('Y').'-'.$wd), strtotime(date('Y').'-'.$wd)+86400]]])->count();

            $rechargeDayTotalMoney[] = RechargeOrder::where(['createtime'=>['between', [strtotime(date('Y').'-'.$wd), strtotime(date('Y').'-'.$wd)+86400]]])->sum('total_fee');
            $rechargeDayTotalNum[] = RechargeOrder::where(['createtime'=>['between', [strtotime(date('Y').'-'.$wd), strtotime(date('Y').'-'.$wd)+86400]]])->count();

        }


        $this->assignconfig('weekDays',$weekDays);
        $this->assignconfig('orderDayTotalMoney',$orderDayTotalMoney);
        $this->assignconfig('orderDayTotalNum',$orderDayTotalNum);
        $this->assignconfig('rechargeDayTotalMoney',$rechargeDayTotalMoney);
        $this->assignconfig('rechargeDayTotalNum',$rechargeDayTotalNum);
        return $this->view->fetch();
    }


}
