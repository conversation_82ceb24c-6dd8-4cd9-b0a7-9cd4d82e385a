<template>
	<view class="package-list p-b-50" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		<view class="list-box p-t-30" v-if="!isEmpty">
			<view class="m-b-30 m-lr-30 p-30 br-10" :style="css.mbg" v-for="item in packageList" :key="item.id">
				<xy-package-row :item="item" />
			</view>
		</view>
		
		<view v-if="!isLoading && isEmpty">
			<view class="no-data">
				<xy-empty text="暂无课程包" />
			</view>
		</view>
		<xy-tabbar />
	</view>
	
	
</template>

<script>
	import { mapState } from 'vuex';
	import xyPackageRow from '@/components/xy-package/row';
	import xyEmpty from '@/components/xy-empty';
	import xyTabbar from '@/components/xy-tabbar';
	
	export default {
		components: {
			xyTabbar,
			xyPackageRow,
			xyEmpty
		},
		data() {
			return {
				css:{},
				isEmpty: true,
				isLoading:true,
				packageList: [],
				currentPage: 1,
				lastPage: 1,
				loadStatus: 'loadmore',
			}
		},
		computed: {
			...mapState(['common'])
		},
		onLoad() {
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		onPullDownRefresh() {
			this.currentPage = 1;
			this.packageList = [];
			this.loadData();
		},
		onReachBottom() {
			if(this.currentPage < this.lastPage) {
				this.currentPage += 1;
				this.loadData();
			}
		},
		methods: {
			loadData(){
				this.$api.get({
					url: '/package/lists',
					loadingTip:'加载中...',
					data: {
						page: this.currentPage,
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.packageList = [...this.packageList, ...res.data];
						this.isEmpty = !this.packageList.length;
						this.isLoading = false;
						this.currentPage = res.current_page; 
						this.lastPage = res.last_page;
						this.loadStatus = this.currentPage < res.last_page ? 'loadmore' : 'nomore';
						
					}
				});
			},
			
		}
	}
</script>

<style scoped lang="scss">
	.no-data{padding-top: 300rpx;}
</style>
