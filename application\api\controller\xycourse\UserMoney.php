<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;

use app\api\model\xycourse\user\Money as UserMoneyModel;

/**
 * XYcourse用户余额接口
 */
class UserMoney extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
    

	/**
	 * 列表
	 */
	public function lists()
    {
    	$params = $this->request->post();
        $data = UserMoneyModel::getLists($params);
        $this->success('账户列表', $data);
    }

    
}