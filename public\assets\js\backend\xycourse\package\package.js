define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'xycourse_vue'], function ($, undefined, Backend, Table, Form, Vue) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xycourse/package/package/index' + location.search,
                    add_url: 'xycourse/package/package/add',
                    edit_url: 'xycourse/package/package/edit',
                    del_url: 'xycourse/package/package/del',
                    multi_url: 'xycourse/package/package/multi',
                    import_url: 'xycourse/package/package/import',
                    table: 'xycourse_package',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'thumbimage', title: __('Thumbimage'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'price', title: __('Price'), operate:'BETWEEN'},
                        {field: 'sales', title: __('Sales')},
                        {field: 'limitday', title: __('Limitday')},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Status normal'),"hidden":__('Status hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            //修改默认弹窗大小
            Fast.config.openArea = ['90%', '90%'];

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        select: function () {
		    // 初始化表格参数配置
		    Table.api.init({
		        extend: {
		            index_url: 'xycourse/package/package/select?store_id='+Config.store_id,
		        }
		    });
            var dataArr = [];
            var multiple = Backend.api.query('multiple');
            multiple = multiple == 'true' ? true : false;
		    var table = $("#table");
		
		    table.on('check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table', function (e, row) {
		        if (e.type == 'check' || e.type == 'uncheck') {
		            row = [row];
		        } else {
		            dataArr = [];
		        }
		        $.each(row, function (i, j) {
		            if (e.type.indexOf("uncheck") > -1) {
		                var index = dataArr.indexOf(j.id);
		                if (index > -1) {
		                    dataArr.splice(index, 1);
		                }
		            } else {
		                dataArr.indexOf(j.id) == -1 && dataArr.push(j);
		            }
		        });
		    });
		
		    // 初始化表格
		    table.bootstrapTable({
		        url: $.fn.bootstrapTable.defaults.extend.index_url,
		        sortName: 'id',
		        showToggle: false,
		        showExport: false,
		        columns: [
		            [
                        {field: 'state', checkbox: multiple, visible: multiple, operate: false},
                        {field: 'id', title: __('Id')},
                        {field: 'thumbimage', title: __('Thumbimage'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'price', title: __('Price'), operate:'BETWEEN'},
                        {field: 'sales', title: __('Sales')},
                        {field: 'limitday', title: __('Limitday')},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Status normal'),"hidden":__('Status hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
		                {
		                    field: 'operate', title: __('Operate'), events: {
		                        'click .btn-chooseone': function (e, value, row, index) {
		                            var multiple = Backend.api.query('multiple');
		                            multiple = multiple == 'true' ? true : false;
                                    var data = multiple ? [row] : row;
                                    Fast.api.close(data);
		                        },
		                    }, formatter: function () {
		                        return '<a href="javascript:;" class="btn btn-danger btn-chooseone btn-xs"><i class="fa fa-check"></i> ' + __('Choose') + '</a>';
		                    }
		                }
		            ]
		        ]
		    });
		
		    // 选中多个
		    $(document).on("click", ".btn-choose-multi", function () {
		        Fast.api.close(dataArr);
		    });
		
		    // 为表格绑定事件
		    Table.api.bindevent(table);
		},
        add: function () {
            var package = new Vue({
                el: "#app",
                data() {
                    return {
                        courseList:[],
                        is_dis:0,//是否参与分销
                        dis_rule:0,//分佣方式
                        commissionRule:[],//佣金规则
                    }
                },
                mounted() {
                    this.getInit(Config.disLevel);
                },
                methods: {
                    getInit(disLevel) {
                        var commissionRule = [];
                        disLevel.forEach((item,index) => {
                            commissionRule.push({level_id:item.id,level_name:item.name, commission_one:0.00,commission_two:0.00});
                        });
                        this.commissionRule = commissionRule;
                    },
                    //是否分销
                    changeDis(value){
                        this.is_dis = value;
                    },
                    //切换分销
                    changeRule(value){
                        this.dis_rule = value;
                    },
                    //选择课程
                    courseSelect(){
                        var that = this;
                        parent.Fast.api.open("xycourse/course/select?multiple=true", __('选择'), {
                            area: ['80%', '80%'],
                            callback: function (data) {
                                var courseList = that.courseList;
                                data.forEach((item,index)=>{
                                    item['num'] = 1;
                                    var flag = false;
                                    courseList.forEach((item1,index1)=>{
                                        if(item1.id == item.id){
                                            flag = true;
                                        }
                                    })
                                    if(!flag){
                                        courseList.push(item);
                                    }
                                });
                                that.courseList = courseList
                            }
                        });
                    },

                    //删除课程
                    deleteCourse(index){
                        this.courseList.splice(index,1);
                    }
                                        
                },
                
            })

            Form.api.bindevent($("form[role=form]"), function(data, ret){
                console.log(data,ret);
            }, function(data, ret){
                console.log(data,ret);
            }, function(success, error){
                //提交之前设置数据
                var courseList = package.courseList;

                if(courseList.length == 0){
                    Toastr.error("请选择关联课程");
                    return false;
                }

                $("#courselist").val(JSON.stringify(courseList));
                $("#commission_rule").val(JSON.stringify(package.commissionRule));
                Form.api.submit($("form[role=form]"), function (data, ret) {
                    setTimeout(function () {
                        parent.Layer.close(parent.Layer.getFrameIndex(window.name));
                        window.parent.location.reload();
                    }, 600);
                });

                return false;
            });
        },
        edit: function () {
            var package = new Vue({
                el: "#app",
                data() {
                    return {
                        courseList:[],
                        is_dis:Config.is_dis,//是否参与分销
                        dis_rule:Config.dis_rule,//分佣方式
                        commissionRule:[],//佣金规则
                    }
                },
                mounted() {
                    this.getInit(Config.courseList);
                    this.getInitDistribution(Config.commission_rule, Config.disLevel);
                },
                methods: {
                    getInitDistribution(commissionRule, disLevel) {
                        var commissionRule = commissionRule ? JSON.parse(commissionRule) : [];
                        disLevel.forEach((item,index) => {
                            var flag = false;
                            commissionRule.forEach((item1,index1)=>{
                                if(item.id == item1.level_id){
                                    flag = true;
                                }
                            });
                            if(!flag){
                                commissionRule.push({level_id:item.id,level_name:item.name, commission_one:0.00,commission_two:0.00});
                            }
                        });
                        this.commissionRule = commissionRule;
                    },
                    //是否分销
                    changeDis(value){
                        this.is_dis = value;
                    },
                    //切换
                    changeRule(value){
                        this.dis_rule = value;
                    },

                    getInit(courseList) {
                        var data = [];
                        courseList.forEach((item,index)=>{
                            data.push({id:item.course.id,name:item.course.name,price:item.course.price,thumbimage:item.course.thumbimage,num:item.num});
                        });
                        this.courseList = data;
                    },

                    //选择课程
                    courseSelect(){
                        var that = this;
                        parent.Fast.api.open("xycourse/course/select?multiple=true", __('选择'), {
                            area: ['80%', '80%'],
                            callback: function (data) {
                                var courseList = that.courseList;
                                data.forEach((item,index)=>{
                                    item['num'] = 1;
                                    var flag = false;
                                    courseList.forEach((item1,index1)=>{
                                        if(item1.id == item.id){
                                            flag = true;
                                        }
                                    })
                                    if(!flag){
                                        courseList.push(item);
                                    }
                                    
                                });
                                that.courseList = courseList
                            }
                        });
                    },

                    //删除课程
                    deleteCourse(index){
                        this.courseList.splice(index,1);
                    }
                                        
                },
                
            })

            Form.api.bindevent($("form[role=form]"), function(data, ret){
                console.log(data,ret);
            }, function(data, ret){
                console.log(data,ret);
            }, function(success, error){
                //提交之前设置数据
                var courseList = package.courseList;

                if(courseList.length == 0){
                    Toastr.error("请选择关联课程");
                    return false;
                }

                $("#courselist").val(JSON.stringify(courseList));
                $("#commission_rule").val(JSON.stringify(package.commissionRule));

                Form.api.submit($("form[role=form]"), function (data, ret) {
                    setTimeout(function () {
                        parent.Layer.close(parent.Layer.getFrameIndex(window.name));
                        window.parent.location.reload();
                    }, 600);
                });

                return false;
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
