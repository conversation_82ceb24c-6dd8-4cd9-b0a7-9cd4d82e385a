define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xycourse/user/course/index' + location.search,
                    add_url: 'xycourse/user/course/add',
                    edit_url: 'xycourse/user/course/edit',
                    del_url: 'xycourse/user/course/del',
                    multi_url: 'xycourse/user/course/multi',
                    import_url: 'xycourse/user/course/import',
                    table: 'xycourse_user_course',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        //{field: 'store.name', title: __('Store_id')},
                        {field: 'user.nickname',operate: 'LIKE', title: __('会员信息'),formatter:function(v,row){
                                var html = '<div style="display:flex"><img width="50px" height="50px" src="'+row.user.avatar+'" /><p style="text-align:left;line-height:20px;margin-top:5px;margin-left:10px">'+row.user.nickname+'(ID:'+row.user.id+')<br/>'+row.user.mobile+'</p></div>';
                                return html;
                            }
                        },
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        //{field: 'type', title: __('Type'), searchList: {"league":__('Type league'),"private":__('Type private')}, formatter: Table.api.formatter.normal},
                        {field: 'thumbimage', title: __('Thumbimage'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'num', title: __('Num')},
                        {field: 'residue', title: __('Residue')},
                        {field: 'duedate', title: __('Duedate'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            //生成链接
            $(document).on("click", ".btn-package", function () {
                Fast.api.open("xycourse/user/course/package",'购买课程包');
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        package: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
