<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div id="app" v-clock>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Grade')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-name" readonly="readonly" class="form-control" type="text" value="{$row.grade_text|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-image" data-rule="required" class="form-control" size="50" name="row[image]" type="text" value="{$row.image|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-image"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-image"></ul>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Commission_one')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-commission_one" data-rule="required" min="0" class="form-control" name="row[commission_one]" type="number" value="{$row.commission_one|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Commission_two')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-commission_two" data-rule="required" min="0" class="form-control" name="row[commission_two]" type="number" value="{$row.commission_two|htmlentities}">
            </div>
        </div>
       
        

    </div>
    
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
    [v-clock] {display: none;}
    .condition-list,upgrade-rules{ line-height: 34px;}
    .condition-list label{margin-right: 20px;border:1px solid #ccc;padding: 0 15px;border-radius: 2px;}
    .condition-list label.active{border-color: blue;}
</style>
