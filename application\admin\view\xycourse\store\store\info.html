<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    
    <div class="panel panel-default">

        <div class="form-group" style="padding-top: 25px;">
            <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Businesshours')}:</label>
            
    
            <div class="col-xs-12 col-sm-8" style="display: flex;">
                <input id="c-openhours" data-rule="required" class="form-control datetimepicker" name="row[open_start]" data-date-format="HH:ss" type="text" value="{$row.businesshours[0]|htmlentities}" style="width: 45%;">
                <div class="control-label" style="width:10%;text-align: center;">-</div>
                <input id="c-openhours" data-rule="required" class="form-control datetimepicker" name="row[open_end]" data-date-format="HH:ss" type="text" value="{$row.businesshours[1]|htmlentities}" style="width: 45%;">
            </div>
           
    
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Service_ids')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-service_ids" data-rule="required" data-source="xycourse/store.service/index" data-multiple="true" class="form-control selectpage" name="row[service_ids]" type="text" value="{$row.service_ids|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Logo')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-logo" data-rule="required" class="form-control" name="row[logo]" type="text" value="{$row.logo|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-logo" class="btn btn-danger faupload" data-input-id="c-logo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-preview-id="p-logo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-logo" class="btn btn-primary fachoose" data-input-id="c-logo" data-mimetype="image/*"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-logo"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-logo"></ul>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Images')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-images" data-rule="required" class="form-control" size="50" name="row[images]" type="textarea" value="{$row.images|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-images" class="btn btn-danger faupload" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-images"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-images"></ul>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Videofile')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-videofile" class="form-control" size="50" name="row[videofile]" type="text" value="{$row.videofile|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-videofile" class="btn btn-danger faupload" data-input-id="c-videofile" data-multiple="false" data-preview-id="p-videofile"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-videofile" class="btn btn-primary fachoose" data-input-id="c-videofile" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-videofile"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-videofile"></ul>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Contacts')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-contacts" data-rule="required" class="form-control" name="row[contacts]" type="text" value="{$row.contacts|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Phone')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-phone" data-rule="required" class="form-control" name="row[phone]" type="text" value="{$row.phone|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Weixin')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-weixin" data-rule="required" class="form-control" name="row[weixin]" type="text" value="{$row.weixin|htmlentities}">
            </div>
        </div>
        
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('City')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class='control-relative'><input id="c-city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[city]" type="text" value="{$row.city|htmlentities}"></div>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-address" data-rule="required" class="form-control" name="row[address]" type="text" value="{$row.address|htmlentities}">
            </div>
        </div>
        
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Latitude')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-latitude" data-rule="required" class="form-control" name="row[latitude]" type="text" value="{$row.latitude|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Longitude')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-longitude" data-rule="required" class="form-control" name="row[longitude]" type="text" value="{$row.longitude|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2"></label>
            <div class="col-xs-12 col-sm-8">
                <a href="https://lbs.qq.com/getPoint/" target="_blank">获取经纬度</a>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
            <div class="col-xs-12 col-sm-8">
                <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50">{$row.content|htmlentities}</textarea>
            </div>
        </div>

    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
