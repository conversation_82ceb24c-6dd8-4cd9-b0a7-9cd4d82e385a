/**
 * 
 * XYcourse 用户管理
 * <AUTHOR>
 * 
 **/
  
import api from '../../utils/request';  
import share from '@/utils/share';
export default {
	namespaced: true,
	state: {
		isLogin: false, // 登录状态
		info: null, // 用户信息
		coach: null, // 老师信息
		staff: null, // 门店员工
		distribution: null, //分销信息
	},
	mutations: {
		setUserInfo(state, data) {
			state.info = data.userInfo;
			uni.setStorageSync("xycourse:user", data.userInfo);
		},
		setDistributionInfo(state, data) {
			state.distribution = data;
		},
		setCoachInfo(state, data) {
			state.coach = data;
		},
		setStaffInfo(state, data) {
			state.staff = data;
		}
	},
	actions: {
		// 登录
		async login({state,commit}, data) {
			
			state.isLogin = true;
			commit('setUserInfo', data);
			
			// 设置分享信息
			share.setShareInfo();
			
			// 存在分享信息，添加分享记录
			var spm = uni.getStorageSync('xycourse:spm');
			
			if (spm) {
				api.post({
					url: '/share/add',
					data: {spm: spm},
					success: res => {
						uni.removeStorageSync('xycourse:spm');
					},
					fail: res =>{
						console.log(res);
					}
				});
			}
		},
		
		async info({commit}, data) {
			commit('setUserInfo', data);
		},
		
		// 门店员工
		async staff({commit}, data) {
			commit('setStaffInfo', data);
		},
		
		// 退出登录
		async logout({state}) {
			state.isLogin = false;
			state.info = null;
			
			// 设置分享信息
			share.setShareInfo();
			uni.removeStorageSync('xycourse:user');
		},
		
		// 获取用户信息
		async getInfo({commit}) {
			return new Promise((resolve, reject) => {
				api.post({
					url: '/user/refresh',
					success: res => {
						commit('setUserInfo', res);
						resolve(res)
					},
					fail: res => {
						reject(res)
					},
				});
			})
		},
		
		// 获取分销商信息
		async getDistributionInfo({commit}) {
			return new Promise((resolve, reject) => {
				api.post({
					url: '/distribution_center/info',
					success: res => {
						commit('setDistributionInfo', res.data);
						resolve(res)
					},
					fail: res => {
						reject(res)
					},
				});
			})
		},
		
		// 获取老师信息
		async getCoachInfo({commit}) {
			return new Promise((resolve, reject) => {
				api.post({
					url: '/coach_center/info',
					success: res => {
						commit('setCoachInfo', res.data);
						resolve(res)
					},
					fail: res => {
						reject(res)
					},
				});
			})
		},
		
		// 获取员工信息
		async getStaffInfo({commit}) {
			return new Promise((resolve, reject) => {
				api.post({
					url: '/staff_center/info',
					success: res => {
						commit('setStaffInfo', res.data);
						resolve(res)
					},
					fail: res => {
						reject(res)
					},
				});
			})
		},
		
	}
};