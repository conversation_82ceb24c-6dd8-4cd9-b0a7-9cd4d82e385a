<?php

namespace app\admin\controller\xycourse;

use app\common\controller\Backend;
use think\Db;
use Exception;
use fast\Random;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 底部导航
 */
class Tabbar extends Backend
{

    /**
     * Tarbar模型对象
     * @var \app\admin\model\xycourse\Tarbar
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
		$this->model = model('app\admin\model\xycourse\Config');
    }

	public function index()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post("data");
            if ($data) {
                try {
                    $config = $this->model->get(['name' => 'tabbar']);
                    if(!$config) {
                        $this->model->allowField(true)->save([
                            'name' => 'tabbar',
                            'title' => '底部导航',
                            'group' => 'tabbar',
                            'type' => 'array',
                            'value' => $data,
                        ]);
                    }else {
                        $config->value = $data;
                        $config->save();
                    }
                    
                } catch (Exception $e) {
                    $this->error($e->getMessage());
                }
                $this->success('保存成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $config = $this->model->where(['name' => 'tabbar'])->value('value');
        $config = json_decode($config, true);
        $this->assignconfig('row', $config);
        return $this->view->fetch();  
    }

    
    //添加
    public function add() {
        return;
    }
    //编辑
    public function edit($ids = null) {
        return;
    }
    //删除
    public function del($ids = null) {
        return;
    }
    //回收站列表
    public function recyclebin() {
        return;
    }
    //回收站(真实删除或清空)
    public function destroy($ids = null) {
        return;
    }
    //回收站还原
    public function restore($ids = null) {
        return;
    }
    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }
	
}
