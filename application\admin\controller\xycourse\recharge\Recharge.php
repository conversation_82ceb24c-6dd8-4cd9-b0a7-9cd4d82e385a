<?php

namespace app\admin\controller\xycourse\recharge;

use app\common\controller\Backend;

/**
 * 充值套餐管理
 *
 * @icon fa fa-circle-o
 */
class Recharge extends Backend
{

    /**
     * Recharge模型对象
     * @var \app\admin\model\xycourse\Recharge
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xycourse\recharge\Recharge;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    
}
