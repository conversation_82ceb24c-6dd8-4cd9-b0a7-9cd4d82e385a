<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_course_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_course_id" data-rule="required" min="0" data-source="user/course/index" class="form-control selectpage" name="row[user_course_id]" type="text" value="{$row.user_course_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coach_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coach_id" data-rule="required" data-source="coach/index" class="form-control selectpage" name="row[coach_id]" type="text" value="{$row.coach_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nums')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nums" data-rule="required" class="form-control" name="row[nums]" type="number" value="{$row.nums|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-type" data-rule="required" class="form-control" name="row[type]" type="text" value="{$row.type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reoson')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-reoson" data-rule="required" class="form-control" name="row[reoson]" type="text" value="{$row.reoson|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
