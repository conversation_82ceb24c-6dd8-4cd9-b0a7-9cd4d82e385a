<template>
	<view class="appointment" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		
		<view class="date-wrapper p-t-20 bc-w m-t-10">
			<DateTabs :value.sync="curDate" :color="css.style.mainColor" :bgColor="css.style.pageModuleBgColor" @change="changeDate" :startDate.sync="startDate" :endDate.sync="endDate"></DateTabs>
		</view>
		
		<view class="tab flex tc tb p-tb-25 bc-w">
			<view v-for="(item,index) in appointmentStatus" :class="'col-'+appointmentStatus.length" @tap="setTab(index)">
				<view :style="appointmentStatusIndex == index ? css.tcmc : css.tcm">
					{{item.name}}
					<view class="line" :style="appointmentStatusIndex == index ? css.mcbg : 'bc-w'"></view>
				</view>
			</view>
		</view>
		
		<view class="appointment-list p-t-30" v-if="!isEmpty">
			<view class="item br-10 m-lr-30 m-b-30 p-30 p-t bc-w" v-for="item in appointmentList" :key="item.id">
				<view class="flex store lh-40">
					<!--text class="ts-26 tc-w p-lr-15 br-10 m-r-15" :style="css.prbg">{{item.type_text}}</text--><text>{{item.date}} {{item.timestr}}</text>
				</view>
				
				<view class="m-t-40 course flex">
					<view class="l">
						<image :src="item.coursethumbimage" class="br-10" />
					</view>
					<view class="r m-l-auto">
						<view class="tb lh-50">{{item.coursename}}</view>
						<view class="ts-28 m-t-15"><text :style="css.tcl">预约编号：</text>{{item.id}}</view>
						<view class="flex lh-50 m-t-25">
							<view class="coach flex">
								<image :src="item.coachheadimage" />
								<text class="m-l-10">{{item.coachrealname}}</text>
							</view>
						</view>
					</view>
				</view>
				
				<view class="action m-t-40 bl-t flex" :style="css.blpc" v-if="item.status == 1">
					<view class="btn bl p-lr-30 ts-28" :style="css.tcm+css.bl" @tap="cancel(item)">
						取消预约
					</view>
					<view class="m-l-auto btn tc-w ts-28 p-lr-50 bl" :style="css.mcbg+css.blc" @tap="verify(item)">
						确认已到店
					</view>
				</view>
			</view>
		</view>
		
		<view v-if="!isLoading && isEmpty">
			<view class="no-data">
				<xy-empty text="暂无预约" />
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import DateTabs from '@/uni_modules/hope-11-date-tabs/components/hope-11-date-tabs/date-tabs.vue'
	import xyEmpty from '@/components/xy-empty';
	export default {
		components: {
			DateTabs,
			xyEmpty
		},
		data() {
			return {
				css:{},
				appointmentStatus:[
					{value:'1',name:'待上课'},
					{value:'2',name:'已完成'},
					{value:'-1',name:'已取消'},
				],
				appointmentStatusIndex:0,
				store_id:0,
				appointmentList: [],
				isEmpty:false,
				isLoading:true,
				currentPage: 1,
				lastPage: 1,
				loadStatus: 'loadmore',
				curDate:'',
				startDate:'',
				endDate:''
			}
		},
		computed: {
			...mapState(['common'])
		},
		onLoad(options) {
			this.$xyfun.setNavBg();
			var curDate = this.$xyfun.getDate();
			this.curDate = curDate;
			this.startDate = this.$xyfun.dateAdd(-30,curDate);
			this.endDate = this.$xyfun.dateAdd(30,curDate)
			this.css = this.$xyfun.css();
			if(options){
				this.curappointmentId = options.appointment_id;
			}
			this.loadData();
		},
		onPullDownRefresh() {
			this.currentPage = 1;
			this.appointmentList = [];
			this.loadData();
		},
		methods: {
			loadData(){
				
				this.$api.post({
					url: '/coach_center/memberAppointment',
					loadingTip:'加载中...',
					data: {
						user_id: this.$Route.query.user_id,
						page: this.currentPage,
						date: this.curDate,
						status: this.appointmentStatus[this.appointmentStatusIndex]['value']
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.isLoading = false;
						this.appointmentList = [...this.appointmentList, ...res.data];
						this.isEmpty = !this.appointmentList.length;
						this.currentPage = res.current_page; 
						this.lastPage = res.last_page;
						this.loadStatus = this.currentPage < res.last_page ? 'loadmore' : 'nomore';
					}
				});
				
			},
			
			//确认
			verify(appointment){
				var that = this;
				var appointmentList = this.appointmentList
				uni.showModal({
					title:'温馨提示',
					content:'亲，确定学员已到店吗？',
					success(e) {
						if(e.confirm){
							that.$api.post({
								url: '/coach_center/verifyAppointment',
								data: {
									id: appointment.id,
									user_id:appointment.user_id,
									store_id: that.store_id
								},
								success: res => {
									that.$xyfun.msg('操作成功');
									appointmentList.forEach((item,index)=>{
										if(item.id == appointment.id){
											appointmentList.splice(index,1);
										}
									})
								}
							});
						}
					}
				})
			},
			
			//取消
			cancel(appointment){
				var that = this;
				var appointmentList = this.appointmentList
				uni.showModal({
					title:'温馨提示',
					content:'亲，确定要取消预约吗？',
					success(e) {
						if(e.confirm){
							that.$api.post({
								url: '/coach_center/cancelAppointment',
								data: {
									id: appointment.id,
									user_id:appointment.user_id,
									store_id: that.store_id
								},
								success: res => {
									that.$xyfun.msg('取消成功');
									appointmentList.forEach((item,index)=>{
										if(item.id == appointment.id){
											appointmentList.splice(index,1);
										}
									})
								}
							});
						}
					}
				})
			},
			
			//切换日期
			changeDate(e) {
				console.log('eeee',e);
				this.curDate = e.dd;
				this.startDate = this.$xyfun.dateAdd(-30,e.dd);
				this.endDate = this.$xyfun.dateAdd(30,e.dd)
				
				console.log('this.startDate',this.startDate);
				console.log('this.endDate',this.endDate);
				
				this.currentPage = 1;
				this.appointmentList = [];
				this.loadData();
			},
			
			setTab(index){
				this.appointmentStatusIndex = index;
				this.currentPage = 1;
				this.appointmentList = [];
				this.loadData();
			},
			
		}
	}
</script>

<style scoped lang="scss">
	.appointment{padding-bottom: 200rpx;}
	
	.tab{
		width: 100%;
		.line{height: 4rpx;width: 60rpx;margin: 10rpx auto 0;}
	}
	
	.appointment-list{
		.item{
			.store{
				image{width: 50rpx;height: 50rpx;border-radius: 25rpx;}
			}
			.course{
				.l, .l image{width: 240rpx;height: 180rpx;}
				.r{
					width: 370rpx;
					.coach image{width: 50rpx;height: 50rpx;border-radius: 25rpx;}
				}
			}
			.btn{height: 54rpx;border-radius: 29rpx;line-height: 54rpx;}
		}
	}
	
</style>