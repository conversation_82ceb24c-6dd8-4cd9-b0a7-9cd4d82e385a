<?php

/**
 * 检测系统必要环境
 */
if (!function_exists('xycourseCheckEnv')) {
    function xycourseCheckEnv($need = [], $is_throw = true)
    {
        $need = is_string($need) ? [$need] : $need;
        //检测是否安装了支付插件
        if (in_array('yansongda', $need)) {
            if (!class_exists(\Yansongda\Pay\Pay::class)) {
                if ($is_throw) {
                    new \addons\xycourse\exception\Exception('请安装微信支付宝整合插件');
                } else {
                    return false;
                }
            }
        }
        return true;
    }
}

/**
 * 获取两个日期相差天数
 */
if(!function_exists('xycourseCountDays')){
    function xycourseCountDays($d1 = '', $d2 = ''){
        $date1=strtotime($d1);
        $date2=strtotime($d2==''?date("Y-m-d"):$d2);
        $a_dt=getdate($date1);
        $b_dt=getdate($date2);
        $a_new=mktime(12,0,0,$a_dt['mon'],$a_dt['mday'],$a_dt['year']);
        $b_new=mktime(12,0,0,$b_dt['mon'],$b_dt['mday'],$b_dt['year']);
        return round(abs($a_new-$b_new)/86400);
    }
}



/**
 * 获取星期
 */
if (!function_exists('xycourseGetWeek')) {
    function xycourseGetWeek($time)
    {
        $weekArr = ['日','一','二','三','四','五','六'];
        $week = date("w",strtotime($time));
        return "周".$weekArr[$week];
    }
}
    

/**
 * 获取最近七天所有日期
 */
if(!function_exists('xycourseGetWeeks')){
    function xycourseGetWeeks($time = '', $format='m-d'){
        $time = ($time != '' ? $time : time());
        //组合数据
        $date = [];
        for ($i=1; $i<=7; $i++){
            $date[] = date($format ,strtotime( '+' . ($i-7) .' days', $time));
        }
        return $date;
    }
}

/**
 * 过滤掉字符串中的 sql 关键字
 */
if (!function_exists('xycourseFilterSql')) {
    function xycourseFilterSql($str)
    {
        $str = strtolower($str);        // 转小写
        $str = str_replace("and", "", $str);
        $str = str_replace("execute", "", $str);
        $str = str_replace("update", "", $str);
        $str = str_replace("count", "", $str);
        $str = str_replace("chr", "", $str);
        $str = str_replace("mid", "", $str);
        $str = str_replace("master", "", $str);
        $str = str_replace("truncate", "", $str);
        $str = str_replace("char", "", $str);
        $str = str_replace("declare", "", $str);
        $str = str_replace("select", "", $str);
        $str = str_replace("create", "", $str);
        $str = str_replace("delete", "", $str);
        $str = str_replace("insert", "", $str);
        $str = str_replace("union", "", $str);
        $str = str_replace("alter", "", $str);
        $str = str_replace("into", "", $str);
        $str = str_replace("'", "", $str);
        $str = str_replace("or", "", $str);
        $str = str_replace("=", "", $str);

        return $str;
    }
}


/**
 * 生成订单号
 * @return string
 */
if (!function_exists('xycourseCreateOrderNo')) {
    function xycourseCreateOrderNo()
    {
        return date('Ymd') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
    }
}
