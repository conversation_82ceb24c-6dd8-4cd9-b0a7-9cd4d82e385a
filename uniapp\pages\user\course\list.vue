<template>
	<view class="course" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		
		<view class="tab flex tc tb p-tb-25 bc-w">
			<view v-for="(item,index) in type" :class="'col-'+type.length" @tap="setTab(index)">
				<view :style="typeIndex == index ? css.tcmc : css.tcm">
					{{item.name}}
					<view class="line" :style="typeIndex == index ? css.mcbg : 'bc-w'"></view>
				</view>
			</view>
		</view>
		<block v-if="!isLoading">
			<view class="course-list p-tb-30" v-if="!isEmpty">
				<view class="item br-10 m-lr-30 m-b-30 p-30 bc-w flex" v-for="item in courseList" :key="item.id">
					<view class="l psr ovh">
						<image :src="$xyfun.image(item.thumbimage)" class="br-10" />
						<!--text :style="css.prbg" class="type ts-24 p-lr-20 p-tb-5 tc-w">{{item.type_text}}</text-->
					</view>
					<view class="r m-l-20">
						<view class="m-t-10 tb">
							{{item.name}}
						</view>
						<view class="m-t-15 flex" :style="css.tcl">
							<view class="ts-28">总次数：{{item.num}}</view>
							<view class="ts-28 m-l-auto">剩余：<text :style="css.tcp">{{item.residue}}次</text></view>
						</view>
						<view class="flex m-t-15 lh-54">
							<view class="ts-28" :style="css.tcl">有效期至：<text :style="css.tcm">{{item.duedate}}</text></view>
						</view>
					</view>
				</view>
			</view>
			<view v-else>
				<xy-empty :text="'暂无'+(typeIndex>0?type[typeIndex].name:'')+'课程'" />
			</view>
		</block>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import xyEmpty from '@/components/xy-empty';
	export default {
		components: {
			xyEmpty,
		},
		data() {
			return {
				css:{},
				isLoading:true,
				isEmpty: true,
				courseList: [],
				currentPage: 1,
				lastPage: 1,
				loadStatus: 'loadmore',
				type:[{name:'全部',value:'all'},{name:'团课',value:'league'},{name:'私教',value:'private'}],
				typeIndex:0,
			}
		},
		computed: {
			...mapState(['common'])
		},
		onLoad(options) {
			if(options.status != undefined){
				this.courseStatusIndex = options.status;
			}
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		onPullDownRefresh() {
			this.currentPage = 1;
			this.courseList = [];
			this.loadData();
		},
		onReachBottom() {
			if(this.currentPage < this.lastPage) {
				this.currentPage += 1;
				this.loadData();
			}
		},
		methods: {
			loadData(){
				this.$api.post({
					url: '/user_course/lists',
					loadingTip:'加载中...',
					data: {
						page: this.currentPage,
						type: this.type[this.typeIndex].value,
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.isLoading = false;
						this.courseList = [...this.courseList, ...res.data];
						this.isEmpty = !this.courseList.length;
						this.currentPage = res.current_page; 
						this.lastPage = res.last_page;
						this.loadStatus = this.currentPage < res.last_page ? 'loadmore' : 'nomore';
					}
				});
				
			},
			
			setTab(index){
				this.typeIndex = index;
				this.currentPage = 1;
				this.courseList = [];
				this.loadData();
			},
			
		}
	}
</script>

<style scoped lang="scss">
	.tab{
		width: 100%;
		.line{height: 4rpx;width: 60rpx;margin: 10rpx auto 0;}
	}
	
	.return{line-height: 40rpx;height: 40rpx;}
	
	.course-list{
		.goods{
			line-height: 150rpx;
			.image{width: 150rpx;height: 150rpx;}
			.image image{width: 150rpx;}
		}
	}
	
	.course-list{
		.item{
			.l, .l image{
				width: 240rpx;height: 180rpx;
			}
			.l .type{position: absolute;left: 0;top: 0;border-radius: 10rpx 0 10rpx 0;}
			.r{
				width: 370rpx;
				.price-box{position: absolute; right: 0;top: 0;}
			}
		}
	}
	
</style>