<template>
	<view class="appointment" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'" v-if="!isLoading">
		
		<view class="tab flex tc tb p-tb-25" :style="css.mbg">
			<view v-for="(item,index) in type" :class="'col-'+type.length" @tap="setTab(index)">
				<view :style="typeIndex == index ? css.tcmc : css.tcm">
					{{item.name}}
					<view class="line" :style="typeIndex == index ? css.mcbg : css.mbg"></view>
				</view>
			</view>
		</view>
		
		<view class="date-wrapper p-t-20 m-t-10"  :style="css.mbg">
			<DateTabs :value.sync="curDate" :color="css.style.mainColor" :bgColor="css.style.pageModuleBgColor" @change="changeDate"></DateTabs>
		</view>
		
		<view class="list-box p-t-30" v-if="!isEmpty">
			<view class="m-b-30 m-lr-30 p-30 br-20 item flex" :style="css.mbg" v-for="item in schedulingList" :key="item.id">
				<view class="l">
					<image :src="item.course.thumbimage" class="br-10" />
				</view>
				<view class="r m-l-auto">
					<view class="tb lh-42">{{item.course.name}}</view>
					<view class="flex m-t-15 lh-54">
						<view :style="css.pbg+css.tcmc" class="br-10 tb ts-26 p-lr-20 br-10">{{item.timestr}}</view>
						<view class="action m-l-auto ts-26 tc bc-h" v-if="item.agreed >= item.people" :style="css.tcl">已约满</view>
						<view class="action m-l-auto ts-26 tc bc-h" v-else-if="currentTime > item.starttime">已完成</view>
						<view class="action m-l-auto ts-26 tc-w tc" v-else :style="css.mcbg" @tap="appointment(item.id)">预约</view>
					</view>
					<view class="flex lh-50 m-t-20">
						<view class="coach flex" v-if="item.coach.id != null">
							<image :src="item.coach.headimage" />
							<text class="m-l-10">{{item.coach.realname}}</text>
						</view>
						<view class="m-l-auto" :style="css.tcl">
							已约<text>{{item.agreed}}</text>/<text>{{item.people}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view v-if="!isLoading && isEmpty">
			<view class="no-data">
				<xy-empty text="暂无可预约信息" />
			</view>
		</view>
		
		<xy-tabbar />
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import DateTabs from '@/uni_modules/hope-11-date-tabs/components/hope-11-date-tabs/date-tabs.vue'
	import xyTabbar from '@/components/xy-tabbar';
	import xyEmpty from '@/components/xy-empty';
	export default {
		components: {
			DateTabs,
			xyTabbar,
			xyEmpty
		},
		data() {
			return {
				css:{},
				type:[{name:'约团课',value:'league'},{name:'约私教',value:'private'}],
				typeIndex:0,
				schedulingList: [],
				isEmpty:false,
				isLoading:true,
				currentPage: 1,
				lastPage: 1,
				loadStatus: 'loadmore',
				curDate:'',
				currentTime:Math.floor(Date.now() / 1000)
			}
		},
		computed: {
			...mapState(['common'])
		},
		onLoad() {
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		onPullDownRefresh() {
			this.currentPage = 1;
			this.schedulingList = [];
			this.loadData();
		},
		onReachBottom() {
			if(this.currentPage < this.lastPage) {
				this.currentPage += 1;
				this.loadData();
			}
		},
		methods: {
			loadData(){
				this.$api.get({
					url: '/scheduling/lists',
					loadingTip:'加载中...',
					data: {
						type:this.type[this.typeIndex].value,
						date:this.curDate,
						page: this.currentPage
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.schedulingList = [...this.schedulingList, ...res.data];
						this.isEmpty = !this.schedulingList.length;
						this.isLoading = false;
						this.currentPage = res.current_page; 
						this.lastPage = res.last_page;
						this.loadStatus = this.currentPage < res.last_page ? 'loadmore' : 'nomore';
					},
					fail(){}
				});
			},
			
			//预约
			appointment(scheduling_id){
				var that = this;
				uni.showModal({
					title:'确定预约',
					content:'亲，确定要预约这个课程吗？',
					success(e) {
						if(e.confirm){
							that.$api.post({
								url: '/appointment/add',
								data: {
									scheduling_id:scheduling_id,
								},
								success: res => {
									that.$xyfun.to('/pages/user/appointment/list');
								},
								fail(){
									
								}
							});
						}
					}
				})
				
			},
			
			//切换日期
			changeDate(e) {
				this.curDate = e.dd;
				this.currentPage = 1;
				this.schedulingList = [];
				this.loadData();
			},
			
			setTab(index){
				this.typeIndex = index;
				this.currentPage = 1;
				this.schedulingList = [];
				this.loadData();
			},
			
		}
	}
</script>

<style scoped lang="scss">
	.appointment{padding-bottom: 200rpx;}
	
	.tab{
		width: 100%;
		.line{height: 4rpx;width: 60rpx;margin: 10rpx auto 0;}
	}
	
	.list-box{
		.item{
			.l, .l image{width: 240rpx;height: 180rpx;}
			.r{
				width: 370rpx;
				.coach image{width: 50rpx;height: 50rpx;border-radius: 25rpx;}
				.action{width: 130rpx;height: 54rpx;line-height: 54rpx;border-radius: 27rpx;}
			}
		}
	}
	
</style>