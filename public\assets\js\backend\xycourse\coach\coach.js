define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xycourse/coach/coach/index' + location.search,
                    add_url: 'xycourse/coach/coach/add',
                    edit_url: 'xycourse/coach/coach/edit',
                    del_url: 'xycourse/coach/coach/del',
                    multi_url: 'xycourse/coach/coach/multi',
                    import_url: 'xycourse/coach/coach/import',
                    table: 'xycourse_coach',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user.nickname',operate: '<PERSON>I<PERSON>', title: __('绑定会员'),formatter:function(v,row){
                                if(v == null){
                                    return '暂未绑定';
                                }else{
                                    var html = '<div style="display:flex"><img width="50px" height="50px" src="'+row.user.avatar+'" /><p style="text-align:left;line-height:20px;margin-top:5px;margin-left:10px">'+row.user.nickname+'(ID:'+row.user.id+')<br/>'+row.user.mobile+'</p></div>';
                                    return html;
                                }
                            }
                        },
                        {field: 'headimage', title: __('Headimage'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'realname', title: __('Realname'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        {field: 'jobnum', title: __('Jobnum'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Status normal'),"hidden":__('Status hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            //修改默认弹窗大小
            Fast.config.openArea = ['80%', '90%'];

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        select: function () {
		    // 初始化表格参数配置
		    Table.api.init({
		        extend: {
		            index_url: 'xycourse/coach/coach/select?store_id='+Config.store_id,
		        }
		    });
            var dataArr = [];
            var multiple = Backend.api.query('multiple');
            multiple = multiple == 'true' ? true : false;
		    var table = $("#table");
		
		    table.on('check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table', function (e, row) {
		        if (e.type == 'check' || e.type == 'uncheck') {
		            row = [row];
		        } else {
		            dataArr = [];
		        }
		        $.each(row, function (i, j) {
		            if (e.type.indexOf("uncheck") > -1) {
		                var index = dataArr.indexOf(j.id);
		                if (index > -1) {
		                    dataArr.splice(index, 1);
		                }
		            } else {
		                dataArr.indexOf(j.id) == -1 && dataArr.push(j.id);
		            }
		        });
		    });
		
		    // 初始化表格
		    table.bootstrapTable({
		        url: $.fn.bootstrapTable.defaults.extend.index_url,
		        sortName: 'id',
		        showToggle: false,
		        showExport: false,
		        columns: [
		            [
                        {field: 'state', checkbox: multiple, visible: multiple, operate: false},
                        {field: 'id', title: __('Id')},
                        {field: 'realname', title: __('Realname'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        {field: 'jobnum', title: __('Jobnum'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
		                {
		                    field: 'operate', title: __('Operate'), events: {
		                        'click .btn-chooseone': function (e, value, row, index) {
		                            var multiple = Backend.api.query('multiple');
		                            multiple = multiple == 'true' ? true : false;
                                    var data = multiple ? [row] : row;
                                    Fast.api.close(data);
		                        },
		                    }, formatter: function () {
		                        return '<a href="javascript:;" class="btn btn-danger btn-chooseone btn-xs"><i class="fa fa-check"></i> ' + __('Choose') + '</a>';
		                    }
		                }
		            ]
		        ]
		    });
		
		    // 选中多个
		    $(document).on("click", ".btn-choose-multi", function () {
		        Fast.api.close(dataArr);
		    });
		
		    // 为表格绑定事件
		    Table.api.bindevent(table);
		},
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
