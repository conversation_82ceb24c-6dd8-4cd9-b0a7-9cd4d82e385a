<template>
	<view class="appointment" :style="css.page+'min-height:'+$xyfun.xysys().windowHeight+'px'">
		<view class="tab flex tc tb p-tb-25 bc-w">
			<view v-for="(item,index) in appointmentStatus" :class="'col-'+appointmentStatus.length" @tap="setTab(index)">
				<view :style="appointmentStatusIndex == index ? css.tcmc : css.tcm">
					{{item.name}}
					<view class="line" :style="appointmentStatusIndex == index ? css.mcbg : 'bc-w'"></view>
				</view>
			</view>
		</view>
		<block v-if="!isLoading">
			<view class="appointment-list p-tb-30" v-if="!isEmpty">
				<view class="item br-10 m-lr-30 m-b-30 p-30 p-t bc-w" v-for="item in appointmentList" :key="item.id">
					<view class="flex store lh-40">
						<!--text class="ts-26 tc-w p-lr-15 br-10 m-r-15" :style="css.prbg">{{item.type_text}}</text--><text>{{item.date}} {{item.timestr}}</text>
						
						<text class="m-l-auto" :style="css.tcp">{{item.status_text}}</text>
					</view>
					
					<view class="m-t-40 course flex">
						<view class="l">
							<image :src="item.coursethumbimage" class="br-10" />
						</view>
						<view class="r m-l-auto">
							<view class="tb lh-50">{{item.coursename}}</view>
							<view class="ts-28 m-t-15"><text :style="css.tcl">预约编号：</text>{{item.id}}</view>
							<view class="flex lh-50 m-t-25">
								<view class="coach flex">
									<image :src="item.coachheadimage" />
									<text class="m-l-10">{{item.coachrealname}}</text>
								</view>
							</view>
						</view>
					</view>
					
					<view class="action m-t-40 bl-t flex" :style="css.blpc" v-if="item.status == 1">
						<view class="btn bl p-lr-30 m-l-auto ts-28" :style="css.tcm+css.bl" @tap="cancel(item.id)">
							取消
						</view>
						<!--view class="btn bl m-l-auto p-lr-30 ts-28 tc-w" :style="css.mcbg" @tap="success(item.id)">
							确认完成
						</view-->
					</view>
					
					<view class="action m-t-40 bl-t flex ts-28" :style="css.blpc" v-if="item.status == -1">
						<text style="css.tcl">取消原因：</text>{{$xyfun.timeFormat(item.ext_arr.cancel_time)}} - {{item.ext_arr.cancel_reason}}
					</view>
					
					
				</view>
			</view>
			<view v-else>
				<xy-empty :text="'暂无'+(appointmentStatusIndex>0?appointmentStatus[appointmentStatusIndex].name:'')+'订单'" />
			</view>
		</block>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import xyEmpty from '@/components/xy-empty';
	export default {
		components: {
			xyEmpty,
		},
		data() {
			return {
				css:{},
				isLoading:true,
				isEmpty: true,
				appointmentList: [],
				currentPage: 1,
				lastPage: 1,
				loadStatus: 'loadmore',
				appointmentStatus:[
					
					{value:'1',name:'待上课'},
					{value:'2',name:'已完成'},
					{value:'-1',name:'已取消'},
				],
				appointmentStatusIndex:0,
			}
		},
		computed: {
			...mapState(['common'])
		},
		onLoad(options) {
			if(options.status != undefined){
				this.appointmentStatusIndex = options.status;
			}
			this.$xyfun.setNavBg();
			this.css = this.$xyfun.css();
			this.loadData();
		},
		onPullDownRefresh() {
			this.currentPage = 1;
			this.appointmentList = [];
			this.loadData();
		},
		onReachBottom() {
			if(this.currentPage < this.lastPage) {
				this.currentPage += 1;
				this.loadData();
			}
		},
		methods: {
			loadData(){
				
				this.$api.post({
					url: '/appointment/lists',
					loadingTip:'加载中...',
					data: {
						page: this.currentPage,
						status: this.appointmentStatus[this.appointmentStatusIndex]['value']
					},
					success: res => {
						uni.stopPullDownRefresh();
						this.isLoading = false;
						this.appointmentList = [...this.appointmentList, ...res.data];
						this.isEmpty = !this.appointmentList.length;
						this.currentPage = res.current_page; 
						this.lastPage = res.last_page;
						this.loadStatus = this.currentPage < res.last_page ? 'loadmore' : 'nomore';
					}
				});
				
			},
			
			//确认完成
			success(id){
				var that = this;
				var appointmentList = this.appointmentList
				uni.showModal({
					title:'温馨提示',
					content:'亲，确定已完成服务吗？',
					success(e) {
						if(e.confirm){
							that.$api.post({
								url: '/appointment/verify',
								data: {
									id: id
								},
								success: res => {
									that.$xyfun.msg('确认成功');
									appointmentList.forEach((item,index)=>{
										if(item.id == id){
											appointmentList.splice(index,1);
										}
									})
								}
							});
						}
					}
				})
			},
			
			cancel(id){
				var that = this;
				var appointmentList = this.appointmentList
				uni.showModal({
					title:'温馨提示',
					content:'亲，确定要取消预约吗？',
					success(e) {
						if(e.confirm){
							that.$api.post({
								url: '/appointment/cancel',
								data: {
									id: id
								},
								success: res => {
									that.$xyfun.msg('取消成功');
									appointmentList.forEach((item,index)=>{
										if(item.id == id){
											appointmentList.splice(index,1);
										}
									})
								}
							});
						}
					}
				})
			},
			
			setTab(index){
				this.appointmentStatusIndex = index;
				this.currentPage = 1;
				this.appointmentList = [];
				this.loadData();
			},
			
		}
	}
</script>

<style scoped lang="scss">
	.tab{
		width: 100%;
		.line{height: 4rpx;width: 60rpx;margin: 10rpx auto 0;}
	}
	
	.return{line-height: 40rpx;height: 40rpx;}
	
	.appointment-list{
		.item{
			.store{
				image{width: 50rpx;height: 50rpx;border-radius: 25rpx;}
			}
			.course{
				.l, .l image{width: 240rpx;height: 180rpx;}
				.r{
					width: 370rpx;
					.coach image{width: 50rpx;height: 50rpx;border-radius: 25rpx;}
				}
			}
			.btn{height: 54rpx;border-radius: 29rpx;line-height: 54rpx;}
		}
	}
	

	
</style>