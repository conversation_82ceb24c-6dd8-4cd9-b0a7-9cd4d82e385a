<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div id="app" v-clock>
        
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Grade')}:</label>
            <div class="col-xs-12 col-sm-8">
                <select  id="c-grade" data-rule="required" class="form-control selectpicker" name="row[grade]">
                    {foreach name="gradeList" item="vo"}
                        <option value="{$key|htmlentities}" {in name="key" value="1"}selected{/in}>{$vo|htmlentities}</option>
                    {/foreach}
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="">
            </div>
        </div>
        
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Discount')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-discount" data-rule="required" class="form-control" placeholder="填写1-10之间的数字，可保留两位小数" step="0.01" name="row[discount]" type="number">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Advancedays')}:</label>
            <div class="col-xs-12 col-sm-5">
                <input id="c-advancedays" data-rule="required" class="form-control" name="row[advancedays]" type="number">
            </div>
            <label class="control-label col-xs-12 col-sm-3" style="text-align: left;">填0则只能当天预定</label>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('dayhour')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-dayhour" placeholder="填写数字，可保留2位小数" data-rule="required" class="form-control" name="row[dayhour]" type="number">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Upgradetype')}:</label>
            <div class="col-xs-12 col-sm-8">
                            
                <select  id="c-upgradetype" data-rule="required" class="form-control selectpicker" name="row[upgradetype]">
                    {foreach name="upgradetypeList" item="vo"}
                        <option value="{$key|htmlentities}" {in name="key" value="or"}selected{/in}>{$vo|htmlentities}</option>
                    {/foreach}
                </select>
    
            </div>
        </div>
    
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Rule')}:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="condition-list">
                    <label v-for="(item,index) in conditionList" :key="index" :class="item.select?'active':''"  @click="onCondition(item)">
                        {{item.name}}
                    </label>
                </div>
            </div>
            <input id="rule" name="row[rule]" type="hidden" />
        </div>
        
        <div class="form-group" v-if="upgradeRules.length > 0">
            <label class="control-label col-xs-12 col-sm-2"></label>
            <div class="col-xs-12 col-sm-8">
                <div class="upgrade-rules">
                    <div v-for="(item,index) in upgradeRules" :key="index" class="form-group">
                        <div class="col-xs-12" style="display: flex;">
                            <div style="line-height: 34px;">{{item.name}}达到: </div>
                            <div class="col-sm-6">
                                <input data-rule="required" min="0" class="form-control" placeholder="请填写数字" v-model="item.value" type="number" >
                            </div>
                        </div>
                     </div>
                </div>
            </div>
        </div>
        
        
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
            <div class="col-xs-12 col-sm-8">
                <textarea id="c-remark" class="form-control " rows="5" name="row[remark]" cols="50"></textarea>
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
            <div class="col-xs-12 col-sm-8">
                
                <div class="radio">
                {foreach name="statusList" item="vo"}
                <label for="row[status]-{$key|htmlentities}"><input id="row[status]-{$key|htmlentities}" name="row[status]" type="radio" value="{$key|htmlentities}" {in name="key" value="normal"}checked{/in} /> {$vo|htmlentities}</label> 
                {/foreach}
                </div>
    
            </div>
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
    [v-clock] {display: none;}
    .condition-list,upgrade-rules{ line-height: 34px;}
    .condition-list label{margin-right: 20px;border:1px solid #ccc;padding: 0 15px;border-radius: 2px;}
    .condition-list label.active{border-color: blue;}
</style>
