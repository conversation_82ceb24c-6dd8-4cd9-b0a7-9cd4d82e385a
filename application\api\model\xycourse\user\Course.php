<?php

namespace app\api\model\xycourse\user;

use app\api\model\xycourse\Course as CourseModel;
use app\api\model\xycourse\user\CourseLog;

use think\Model;


class Course extends Model
{

    

    

    // 表名
    protected $name = 'xycourse_user_course';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_text',
    ];

    public function getTypeList()
    {
        return ['league' => __('团课'), 'private' => __('私教')];
    }

    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 列表
     */
    public static function getLists($params)
    {
        extract($params);
        $where = ['user_id'=>$user_id];
        if($type != 'all'){
            $where['type'] = $type;
        }
        $list = self::where($where)->order('id desc')->paginate();
        return $list;
    }

    // 更新记录
    public static function change($user_package_id,$course_id,$user_id,$num,$type,$operate_user_id = 0,$duedate = '',$user_course_id = 0) {


        if($user_course_id){
            $userCourse = self::get($user_course_id);
        }else{
            $userCourse = null;
        }

        if(empty($userCourse)){

            $course = CourseModel::get($course_id);
            $userCourse = self::create([
                'course_id'         => $course_id,
                'store_id'          => 1,
                'user_id'           => $user_id,
                'user_package_id'   => $user_package_id,
                'type'              => $course['type'],
                'name'              => $course['name'],
                'thumbimage'        => $course['thumbimage'],
                'num'               => $num,
                'residue'           => $num,
                'duedate'           => $duedate
            ]);

        }else{
            $userCourse->residue = $userCourse['residue'] + $num;
            $userCourse->save();
        }

        // 写入日志
        CourseLog::create(['user_id' => $user_id,'store_id' => 1, 'user_package_id'   =>$user_package_id, 'user_course_id' => $userCourse['id'], 'operate_user_id' => $operate_user_id, 'num' => $num, 'type' => $type]);

        return true;
    }
    







}
