<?php

namespace app\api\model\xycourse\user;

use think\Model;
use app\api\model\xycourse\user\User;
use app\api\model\xycourse\Config;
use addons\xycourse\exception\Exception;
use addons\xycourse\service\Distribution as DistributionService;
use addons\xycourse\service\Store as StoreService;
use app\api\model\xycourse\user\Account;
use think\Db;

class Withdraw extends Model
{

    // 表名
    protected $name = 'xycourse_user_withdraw';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'type_text',
        'audit_time_text',
        'payment_time_text',
        'status_text'
    ];

    /**
     * 初始化用户提现信息
     */

    public static function getWithdrawInit($params)
    {
        
        extract($params);
       
        $config = Config::getValueByName('withdraw');
        $user = User::info();
        $account = Account::where(['user_id'=>$user->id,'is_default'=>1])->find();
        
        switch ($type) {
            case 'distribution':
                $distributionService =  new DistributionService($user->id);
                $distributionInfo = $distributionService->distribution;
                $config['able'] = $distributionInfo['commission'];
                $config['account'] = $account;
                break;
            
            case 'balance':
                $config['able'] = $user->money;
                $config['text'] = '余额';
                $config['account'] = $account;
                $response['status'] = 'normal';
                $response['msg'] = '申请提现';
                $response['data'] = $config;
                break;
            default :
                new Exception('不支持该提现类型');
                break;
            
        }

        return $config;
       
    }

    /**
     * 列表
     */
    public static function getList($params)
    {
        extract($params);
        $user = User::info();

        $where = ['user_id'=>$user->id,'type'=>$type];

        $list = self::where($where)->order('id desc')->paginate();
        return $list;
    }

    // 取消订单
    public static function cancel($params)
    {
        $user = User::info();
        extract($params);
        $withdraw = self::where(['user_id'=>$user->id,'id'=>$id,'status'=>0])->find($id);
        if (!$withdraw) {
            new Exception('不能取消');
        }
        $withdraw->status = -2;  
        $withdraw->save();

        User::money($withdraw->apply_money,$user->id,'cancel_withdraw');

        return $withdraw;
    }

    /**
     * 添加
     */
    public static function add($params){
       
        $user = User::info();

        //提现配置获取手续费
        $config = Config::getValueByName('withdraw');

        //提现金额
        $applyMoney = $params['apply_money'];

        if($applyMoney <= 0){
            new Exception('提现金额要大于0');
        }

        if($applyMoney < $config['min']){
            new Exception('提现金额不能低于'.$config['min']);
        }

        if($applyMoney > $config['max']){
            new Exception('单次提现金额不能大于'.$config['max']);
        }

        //手续费比例
        $rate = $config['rate'];

        //手续费
        $serviceMoney = $applyMoney * $rate / 100;

        //实际转账金额
        $money = $applyMoney - $serviceMoney;

        $withdrawData['user_id'] = $user->id;
        $withdrawData['withdraw_sn'] = xycourseCreateOrderNo();
        $withdrawData['type'] = $params['type'];
        $withdrawData['apply_money'] = $applyMoney;
        $withdrawData['rate'] = $rate;
        $withdrawData['service_money'] = $serviceMoney;
        $withdrawData['money'] = $money;
        $withdrawData['realname'] = $params['realname'];
        $withdrawData['mobile'] = $params['mobile'];
        $withdrawData['account_type'] = $params['account_type'];
        $withdrawData['account_name'] = $params['account_name'];
        $withdrawData['account_no'] = $params['account_no'];

        $withdraw = Db::transaction(function () use ($config,$withdrawData,$user) {
            
            //创建提现订单
            $withdraw = self::create($withdrawData);
            switch ($withdrawData['type']) {
                case 'distribution':
                    //分销商佣金变更
                    $distribution =  new DistributionService($withdrawData['user_id']);
                    $distribution->commission(-$withdrawData['apply_money'],'apply_withdraw',$withdraw->id);
                    break;
            }

            return true;
        });

        return $withdraw;

    }
    

    
    public function getTypeList()
    {
        return ['coach' => __('老师提现'), 'distribution' => __('分销商提现')];
    }

    public function getStatusList()
    {
        return ['0' => __('待审核'),'1' => __('待转账'),'2' => __('已转账'),'-1' => __('已拒绝'),'-2' => __('已取消')];
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getAuditTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['audit_time']) ? $data['audit_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getPaymentTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['payment_time']) ? $data['payment_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setAuditTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setPaymentTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


}
