<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;
use app\api\model\xycourse\Scheduling as SchedulingModel;

/**
 * XYcourse 排班接口
 */
class Scheduling extends Api
{
    protected $noNeedLogin = ['lists'];
    protected $noNeedRight = ['*'];

	/**
	 * 排班列表
	 */
	public function lists()
    {
    	$params = $this->request->get();
        $data = SchedulingModel::getLists($params);
        $this->success('排班列表', $data);
    }

}