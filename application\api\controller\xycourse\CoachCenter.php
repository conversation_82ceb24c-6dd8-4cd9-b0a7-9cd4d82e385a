<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;
use addons\xycourse\service\Coach as CoachService;
use app\api\model\xycourse\Appointment as AppointmentModel;
use app\api\model\xycourse\user\Course as UserCourseModel;
use app\api\model\xycourse\user\User as UserModel;

/**
 * 老师中心接口
 */
class CoachCenter extends Api
{
    protected $noNeedLogin = ['lists','detail'];
    protected $noNeedRight = ['*'];
    

    protected $coachService = null;

    public function _initialize()
	{
	    parent::_initialize();

        $coachService = new CoachService($this->auth->id);
        $statusArr = $coachService->getCoachStatus();
        if($statusArr['status'] !== 'normal'){
            $this->error($statusArr['msg']);
        }

        $this->coachService = $coachService;

	}

    /**
     * 预约确认
     */
    public function verifyAppointment()
    {
        $params = $this->request->post();
        $params['coach_id'] = $this->coachService->coach->id;
        $data = AppointmentModel::verifyAppointment($params);
        $this->success('操作成功', $data);
    }

    /**
     * 会员详情
     */
    public function member() {
        $user_id = $this->request->post('user_id');
        $userInfo = UserModel::where(['id'=>$user_id])->find();
        if(empty($userInfo)){
            $this->error('会员不存在');
        }
        $this->success('会员详情', $userInfo);
    }

    /**
     * 课程核销
     */
    public function verifyCourse() {
        $params = $this->request->post();
        $userCourseInfo = UserCourseModel::get($params['user_course_id']);
        if(empty($userCourseInfo)){
            $this->error('会员课程不存在');
        }

        if($userCourseInfo['residue'] < $params['num']){
            $this->error('剩余次数不足');
        }

        UserCourseModel::change($userCourseInfo['user_package_id'],$userCourseInfo['course_id'],$userCourseInfo['user_id'],-$params['num'],'verify',$this->coachService->coach->user_id);

        $this->success('操作成功');
    }

    /**
     * 会员预约
     */
    public function memberAppointment() {
        $params = $this->request->post();
        $params['coach_id'] = $this->coachService->coach->id;
        $data = AppointmentModel::getLists($params);
        $this->success('预约列表', $data);
    }

    /**
     * 会员课程
     */
    public function memberCourse(){
        $params = $this->request->post();
        $data = UserCourseModel::getLists($params);
        $this->success('预约列表', $data);
    }

    /**
     * 课程预约
     */
    public function appointment() {
        $params = $this->request->post();
        $params['coach_id'] = $this->coachService->coach->id;
        $data = AppointmentModel::getLists($params);
        $this->success('预约列表', $data);
    }

    /**
     * 取消预约
     */
    public function cancelAppointment() {
        $params = $this->request->post();
        $params['coach_id'] = $this->coachService->coach->id;
        $data = AppointmentModel::cancelAppointment($params);
        $this->success('取消成功', $data);
    }

    /**
     * 老师中心
     */
    public function info()
    {
        $coachInfo = $this->coachService->coach;
        $this->success('老师详情', $coachInfo);
    }
	
	
}