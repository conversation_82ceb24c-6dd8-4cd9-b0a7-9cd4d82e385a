<?php

namespace app\api\model\xycourse;

use think\Model;

class Level extends Model
{

    // 表名
    protected $name = 'xycourse_level';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'grade_text',
        'upgradetype_text',
        'status_text'
    ];

    /**
     * 列表
     */
    public static function getList($params)
    {
        extract($params);
        $where = [
            'status' => 'normal',
        ];
        
        $order = 'grade asc';

        $data = self::where($where)->order($order);

        if (isset($page)) {
            $data = $data->paginate();
        } else {
            if(isset($limit)){
                $data = $data->limit($limit)->select();
            }else{
                $data = $data->select();
            }
        }

        return $data;
    }
    
    //条件列表
    public function getConditionList()
    {
        return ['c1' => __('单次充值金额')];
    }
    
    public function getGradeList()
    {
        return ['1' => __('Grade 1'), '2' => __('Grade 2'), '3' => __('Grade 3'), '4' => __('Grade 4'), '5' => __('Grade 5'), '6' => __('Grade 6'), '7' => __('Grade 7'), '8' => __('Grade 8'), '9' => __('Grade 9'), '10' => __('Grade 10')];
    }

    public function getUpgradetypeList()
    {
        return ['or' => __('Upgradetype or'), 'and' => __('Upgradetype and')];
    }

    public function getStatusList()
    {
        return ['normal' => __('Status normal'), 'hidden' => __('Status hidden')];
    }


    public function getGradeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['grade']) ? $data['grade'] : '');
        $list = $this->getGradeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getUpgradetypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['upgradetype']) ? $data['upgradetype'] : '');
        $list = $this->getUpgradetypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

}
