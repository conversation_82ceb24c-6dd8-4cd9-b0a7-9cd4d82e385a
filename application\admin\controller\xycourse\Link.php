<?php

namespace app\admin\controller\xycourse;

use app\common\controller\Backend;
use app\admin\model\xycourse\Article as ArticleModel;

/**
 * 链接管理
 *
 * @icon fa fa-circle-o
 */
class Link extends Backend
{

    /**
     * Link模型对象
     * @var \app\admin\model\xycourse\Link
     */
    protected $model = null;
    protected $searchFields = 'id,name,type';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xycourse\Link;
        $this->view->assign("typeList", $this->model->getTypeList());
    }

    /**
     * 生成链接
     */
    public function load(){
        //基础链接
        $basicLink = [
            ['name'=>'首页','url'=>'/pages/index','type'=>'basic'],
            ['name'=>'约课','url'=>'/pages/appointment','type'=>'basic'],
            ['name'=>'购课','url'=>'/pages/package','type'=>'basic'],
            ['name'=>'我的','url'=>'/pages/user','type'=>'basic'],
            ['name'=>'门店介绍','url'=>'/pages/store/detail','type'=>'basic'],
            ['name'=>'优惠券领取','url'=>'/pages/coupon/list','type'=>'basic'],
            ['name'=>'分享','url'=>'share','type'=>'basic'],
            
        ];

        //会员中心
        $userLink = [
            ['name'=>'我的预约','url'=>' /pages/user/appointment/list','type'=>'user'],
            ['name'=>'我的课程','url'=>'/pages/user/course/list','type'=>'user'],
            ['name'=>'我的订单','url'=>'/pages/user/order/list','type'=>'user'],
            ['name'=>'我的优惠券','url'=>'/pages/user/coupon/list','type'=>'user'],
            ['name'=>'分销中心','url'=>'/pages/distribution/center','type'=>'user'],
            ['name'=>'我的余额','url'=>'/pages/user/balance/detail','type'=>'user'],
            ['name'=>'充值','url'=>'/pages/user/balance/recharge','type'=>'user'],
            ['name'=>'我的资料','url'=>'/pages/user/info','type'=>'user'],
            ['name'=>'老师中心','url'=>'/pages/coach/center','type'=>'user'],
            ['name'=>'员工中心','url'=>'/pages/staff/center','type'=>'user'],
            
        ];
        
        //文章链接
        $articleLink = [];
        $articleList = ArticleModel::where(['status'=>'normal'])->select();
        foreach($articleList as $c){
            $articleLink[] = ['name'=>$c['title'],'url'=>'/pages/article/detail?id='.$c['id'],'type'=>'article'];
        }
        $allLink = array_merge($basicLink,$userLink,$articleLink);
        foreach($allLink as $l){
            $link = $this->model->where($l)->find();
            if(!$link){
                $this->model->create($l);
            }
        }
        $this->success('生成成功');
    }

    /**
     * 选择链接
     */
    public function select()
    {
        if ($this->request->isAjax()) {
            return $this->index();
        }
        return $this->view->fetch();
    }

    //回收站列表
    public function recyclebin() {
        return;
    }
    //回收站(真实删除或清空)
    public function destroy($ids = null) {
        return;
    }
    //回收站还原
    public function restore($ids = null) {
        return;
    }
    //批量操作(修改状态)
    public function multi($ids = null) {
        return;
    }

}