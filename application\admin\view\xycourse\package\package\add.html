<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div id="app" v-clock>

        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">基础信息</div>
                <div class="row mt-15">
                    <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
                    <div class="col-xs-12 col-sm-4">
                        <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="">
                    </div>

                    <label class="control-label col-xs-12 col-sm-1">{:__('Price')}:</label>
                    <div class="col-xs-12 col-sm-4">
                        <input id="c-price" data-rule="required" class="form-control" step="0.01" name="row[price]" type="number" value="">
                    </div>
                </div>
                <div class="row mt-15">
                    <label class="control-label col-xs-12 col-sm-2">{:__('Virtualsales')}:</label>
                    <div class="col-xs-12 col-sm-4">
                        <input id="c-virtualsales" data-rule="required" class="form-control" name="row[virtualsales]" type="number" value="0">
                    </div>

                    <label class="control-label col-xs-12 col-sm-1">{:__('Limitday')}:</label>
                    <div class="col-xs-12 col-sm-4">
                        <input id="c-limitday" data-rule="required" class="form-control" name="row[limitday]" type="number" >
                    </div>
                </div>
                <div class="row mt-15">
                    <label class="control-label col-xs-12 col-sm-2">{:__('Thumbimage')}:</label>
                    <div class="col-xs-12 col-sm-4">
                        <div class="input-group">
                            <input id="c-thumbimage" data-rule="required" class="form-control" size="50" name="row[thumbimage]" type="text">
                            <div class="input-group-addon no-border no-padding">
                                <span><button type="button" id="faupload-thumbimage" class="btn btn-danger faupload" data-input-id="c-thumbimage" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-thumbimage"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                <span><button type="button" id="fachoose-thumbimage" class="btn btn-primary fachoose" data-input-id="c-thumbimage" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                            </div>
                            <span class="msg-box n-right" for="c-thumbimage"></span>
                        </div>
                        <ul class="row list-inline faupload-preview" id="p-thumbimage"></ul>
                    </div>

                    <label class="control-label col-xs-12 col-sm-1">{:__('Images')}:</label>
                    <div class="col-xs-12 col-sm-4">
                        <div class="input-group">
                            <input id="c-images" data-rule="required" class="form-control" size="50" name="row[images]" type="textarea">
                            <div class="input-group-addon no-border no-padding">
                                <span><button type="button" id="faupload-images" class="btn btn-danger faupload" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                <span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                            </div>
                            <span class="msg-box n-right" for="c-images"></span>
                        </div>
                        <ul class="row list-inline faupload-preview" id="p-images"></ul>
                    </div>
                </div>
                <div class="row mt-15">
                    <label class="control-label col-xs-12 col-sm-2">{:__('关联课程')}:</label>
                    <div class="col-xs-12 col-sm-9">
                        <div class="btn btn-success" @click="courseSelect()"><i class="fa fa-search"></i> 选择课程</div>
                        <div class="row course-list">
                            <div class="item flex" v-for="(item, index) in courseList">
                                <div class="flex l">
                                    <img :src="item.thumbimage" />
                                    <div>
                                        <div class="course-name">{{ item.name }}</div>
                                        <div>单价:{{ item.price }}</div>
                                    </div>
                                </div>
                                <input data-rule="required" class="form-control" v-model="courseList[index]['num']" type="number" step="1" placeholder="请输入数量">
                                <div class="unit">次</div>
                                <div class="del" @click="deleteCourse(index)">
                                    X
                                </div>
                            </div>
                        </div>
                    </div>
                    <input id="courselist" name="row[courselist]" type="hidden" />
                </div>
                <div class="row mt-15">
                    <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
                    <div class="col-xs-12 col-sm-9">
                        <textarea id="c-content" data-rule="required" class="form-control editor" rows="5" name="row[content]" cols="50"></textarea>
                    </div>
                </div>

                <div class="row mt-15 mb-15">
                    <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
                    <div class="col-xs-12 col-sm-8">
                        
                        <div class="radio">
                        {foreach name="statusList" item="vo"}
                        <label for="row[status]-{$key|htmlentities}"><input id="row[status]-{$key|htmlentities}" name="row[status]" type="radio" value="{$key|htmlentities}" {in name="key" value="normal"}checked{/in} /> {$vo|htmlentities}</label> 
                        {/foreach}
                        </div>
            
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">分销信息</div>

                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">是否参与分销:</label>
                    <div class="col-xs-12 col-sm-8">
                        <div class="radio">
                            {foreach name="isDisList" item="vo"}
                            <label @click="changeDis({$key|htmlentities})"  for="row[is_dis]-{$key|htmlentities}"><input id="row[is_dis]-{$key|htmlentities}" name="row[is_dis]" type="radio" value="{$key|htmlentities}" {in name="key" value="0"}checked{/in} />{$vo|htmlentities}</label> 
                            {/foreach}
                        </div>
                    </div>
                </div>
        
                <div v-if="is_dis == 1" class="form-group">
                    <label class="control-label col-xs-12 col-sm-2">佣金规则:</label>
                    <div class="col-xs-12 col-sm-8">
                        <div class="radio">
                            {foreach name="disRuleList" item="vo"}
                            <label @click="changeRule({$key|htmlentities})"  for="row[dis_rule]-{$key|htmlentities}"><input id="row[dis_rule]-{$key|htmlentities}" name="row[dis_rule]" type="radio" value="{$key|htmlentities}" {in name="key" value="0"}checked{/in} /> {$vo|htmlentities}</label> 
                            {/foreach}
                        </div>
                    </div>
                </div>
        
                <div v-if="is_dis == 1 && dis_rule == 1" class="form-group">
                    <div class="row mt-15 item">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <input id="commission_rule" name="row[commission_rule]" type="hidden" />
                        <div class="col-xs-12 col-sm-8">
                            <table class="table-box">
                                <tr>
                                    <td>分销等级</td>
                                    <td>一级佣金比例（%）</td>
                                    <td>二级佣金比例（%）</td>
                                </tr>
                                <tr v-for="(item, k) in commissionRule">
                                    <td>{{item.level_name}}</td>
                                    <td>
                                        <input v-enter-number maxlength="22"  class="form-control" v-model="item.commission_one" />
                                    </td>
                                    <td>
                                        <input v-enter-number maxlength="22"  class="form-control" v-model="item.commission_two" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    
        
    
        

        

        
    </div>

    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
    [v-clock] {display: none;}
    .mt-15{margin-top: 15px;}
    .mb-15{margin-bottom: 15px;}
    .flex{display: flex;flex-wrap: wrap;}
    .course-list{justify-content: center;align-items: center;}
    .course-list .item{border: solid 1px #d2d6de; margin: 15px;padding: 15px;}
    .course-list .item .l{width: 300px;}
    .course-list .course-name{font-size: 16px;font-weight: bold;padding: 5px 0 2px;}
    .course-list .item input{width: 150px; margin-top: 15px; margin-left: 30px;}
    .course-list .item img{width: 80px;height: 60px; margin-right: 10px;}
    .course-list .item .del{font-size: 20px;color:#d2d6de ; padding: 15px;margin-left: auto;cursor:default;}
    .course-list .unit{margin: 20px 0 0 8px;}

    .table-box{border: solid 1px #ddd;width: 100%;}
    .table-box th{font-weight: bold;}
    .table-box td{padding: 10px 20px;border-top:  solid 1px #ddd;border-right:  solid 1px #ddd;}
</style>
