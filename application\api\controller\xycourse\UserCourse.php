<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;

use app\api\model\xycourse\user\Course as UserCourseModel;

/**
 * XYcourse用户课程接口
 */
class UserCourse extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
    

	/**
	 * 列表
	 */
	public function lists()
    {
    	$params = $this->request->post();
        $params['user_id'] = $this->auth->id;
        $data = UserCourseModel::getLists($params);
        $this->success('账户列表', $data);
    }

    
}