<?php

namespace app\api\controller\xycourse;
use app\common\controller\Api;
use app\api\model\xycourse\recharge\Recharge as RechargeModel;

/**
 * XYcourse 充值套餐接口
 */
class Recharge extends Api
{
    protected $noNeedLogin = ['lists'];
    protected $noNeedRight = ['*'];

	/**
	 * 充值套餐列表
	 */
	public function lists()
    {
    	$params = $this->request->get();
        $data = RechargeModel::getLists($params);
        $this->success('充值套餐列表', $data);
    }

}